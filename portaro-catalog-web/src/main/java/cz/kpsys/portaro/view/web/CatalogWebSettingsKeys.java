package cz.kpsys.portaro.view.web;

import cz.kpsys.portaro.commons.validation.nullablenotblank.NullableNotBlank;
import cz.kpsys.portaro.commons.web.HtmlLink;
import cz.kpsys.portaro.layout.json.GenericLayoutSettingHolder;
import cz.kpsys.portaro.setting.SettingKey;
import lombok.NonNull;

import java.util.List;

import static cz.kpsys.portaro.app.CatalogConstants.Ini.SECTION_SUTOR_SUTIN;

public class CatalogWebSettingsKeys {
    public static final String SECTION_GUI_THEMING = "gui.theming";
    public static final String SECTION_GUI_COMPONENTS = "gui.components";
    public static final String SECTION_GUI_DOCUMENT_TABPRIORITY = "gui.document.tabPriority";
    public static final String SECTION_GUI_LAYOUTS = "gui.layouts";
    public static final String SECTION_GUI_BARCODE_CARDS = "gui.barcodeCards";

    public static final SettingKey<@NonNull String> HEADER_BACKGROUND_COLOR = new SettingKey<>(SECTION_GUI_THEMING, "headerBackgroundColor");
    public static final SettingKey<@NonNull String> HEADER_TEXT_COLOR = new SettingKey<>(SECTION_GUI_THEMING, "headerTextColor");
    public static final SettingKey<@NonNull String> HEADER_LINK_COLOR = new SettingKey<>(SECTION_GUI_THEMING, "headerLinkColor");
    public static final SettingKey<@NonNull String> MAIN_MENU_BACKGROUND_COLOR = new SettingKey<>(SECTION_GUI_THEMING, "mainMenuBackgroundColor");
    public static final SettingKey<@NonNull String> MAIN_MENU_TEXT_COLOR = new SettingKey<>(SECTION_GUI_THEMING, "mainMenuTextColor");
    public static final SettingKey<@NullableNotBlank String> MAIN_MENU_HIGHLIGHT_BACKGROUND_COLOR = new SettingKey<>(SECTION_GUI_THEMING, "mainMenuHighlightBackgroundColor");
    public static final SettingKey<@NullableNotBlank String> MAIN_MENU_HIGHLIGHT_TEXT_COLOR = new SettingKey<>(SECTION_GUI_THEMING, "mainMenuHighlightTextColor");
    public static final SettingKey<@NonNull String> GLOBAL_SEARCH_BUTTON_COLOR = new SettingKey<>(SECTION_GUI_THEMING, "globalSearchButtonColor");
    public static final SettingKey<@NonNull String> TABLE_HEADER_ACCENT_COLOR = new SettingKey<>(SECTION_GUI_THEMING, "tableHeaderAccentColor");
    public static final SettingKey<@NonNull String> SELECTED_TAB_HIGHLIGHT_COLOR = new SettingKey<>(SECTION_GUI_THEMING, "selectedTabHighlightColor");
    public static final SettingKey<@NonNull List<HtmlLink>> SUB_SEARCH_LINKS = new SettingKey<>(SECTION_GUI_COMPONENTS, "subSearchLinks");
    public static final SettingKey<@NonNull Integer> EXEMPLARS = new SettingKey<>(SECTION_GUI_DOCUMENT_TABPRIORITY, "exemplars");
    public static final SettingKey<@NonNull Integer> ARTICLES = new SettingKey<>(SECTION_GUI_DOCUMENT_TABPRIORITY, "articles");
    public static final SettingKey<@NonNull Integer> PARTS = new SettingKey<>(SECTION_GUI_DOCUMENT_TABPRIORITY, "parts");
    public static final SettingKey<@NonNull Integer> CITATION = new SettingKey<>(SECTION_GUI_DOCUMENT_TABPRIORITY, "citation");
    public static final SettingKey<@NonNull Integer> NONE = new SettingKey<>(SECTION_GUI_DOCUMENT_TABPRIORITY, "none");
    public static final SettingKey<@NonNull Integer> TOC = new SettingKey<>(SECTION_GUI_DOCUMENT_TABPRIORITY, "toc");
    public static final SettingKey<@NonNull Integer> DETAIL = new SettingKey<>(SECTION_GUI_DOCUMENT_TABPRIORITY, "detail");
    public static final SettingKey<@NonNull Integer> MARC = new SettingKey<>(SECTION_GUI_DOCUMENT_TABPRIORITY, "marc");
    public static final SettingKey<@NonNull Integer> LOANS = new SettingKey<>(SECTION_GUI_DOCUMENT_TABPRIORITY, "loans");
    public static final SettingKey<@NonNull Integer> OPERATIONS = new SettingKey<>(SECTION_GUI_DOCUMENT_TABPRIORITY, "operations");

    // Layouts
    public static final SettingKey<GenericLayoutSettingHolder> LAYOUT_RECORD_DETAIL = new SettingKey<>(SECTION_GUI_LAYOUTS, "recordDetail");
    public static final SettingKey<@NonNull GenericLayoutSettingHolder> LAYOUT_DOCUMENT_DETAIL = new SettingKey<>(SECTION_GUI_LAYOUTS, "documentDetail");
    public static final SettingKey<@NonNull GenericLayoutSettingHolder> LAYOUT_AUTHORITY_DETAIL = new SettingKey<>(SECTION_GUI_LAYOUTS, "authorityDetail");
    public static final SettingKey<@NonNull GenericLayoutSettingHolder> LAYOUT_USER_ACCOUNT = new SettingKey<>(SECTION_GUI_LAYOUTS, "userAccount");

    // SUTOR/SUTIN
    public static final SettingKey<@NonNull Boolean> SUTOR_SUTIN_LAYOUT_ENABLED = new SettingKey<>(SECTION_SUTOR_SUTIN, "sutinLayout");

    // BARCODE CARDS
    public static final SettingKey<@NonNull Boolean> BARCODE_CARDS_ENABLED = new SettingKey<>(SECTION_GUI_BARCODE_CARDS, "enabled");
    public static final SettingKey<@NonNull String> BARCODE_CARD_BACKGROUND_COLOR = new SettingKey<>(SECTION_GUI_BARCODE_CARDS, "cardBackgroundColor");
    public static final SettingKey<@NonNull String> BARCODE_CARD_TEXT_COLOR = new SettingKey<>(SECTION_GUI_BARCODE_CARDS, "cardTextColor");
    public static final SettingKey<@NonNull String> BARCODE_CARD_LABEL_COLOR = new SettingKey<>(SECTION_GUI_BARCODE_CARDS, "cardLabelColor");
}