dependencies {
    testImplementation(project(":portaro-test-environment"))
    testImplementation("org.junit.jupiter:junit-jupiter:+")
    testRuntimeOnly("org.junit.platform:junit-platform-launcher")
    testImplementation("org.mockito:mockito-core:+")
    testImplementation("org.apache.commons:commons-lang3:3.+")

    compileOnly("org.projectlombok:lombok:+")
    annotationProcessor("org.projectlombok:lombok:+")

    implementation(project(":portaro-auth"))
    implementation(project(":portaro-catalog"))
    implementation(project(":portaro-commons"))
    implementation(project(":portaro-core"))
    implementation(project(":portaro-user"))
    implementation(project(":portaro-web"))
    implementation(project(":portaro-catalog-web"))

    // Deps for wallet pass generation
    api("de.brendamour:jpasskit:0.4.2")
    implementation("com.google.auth:google-auth-library-oauth2-http:1.36.0")
    implementation("com.auth0:java-jwt:4.5.0")

    implementation("org.springframework:spring-core:6.+")
    implementation("org.springframework:spring-web:6.+")
    implementation("org.springframework:spring-context-support:6.+")
    implementation("org.springframework.security:spring-security-core:6.+")
    implementation("org.springframework.boot:spring-boot:3.+")
    implementation("org.slf4j:slf4j-api:+")
    implementation("com.fasterxml.jackson.core:jackson-databind:2.+")
    implementation("jakarta.validation:jakarta.validation-api:3.+")
    implementation("com.github.kagkarlsson:db-scheduler-spring-boot-starter:+")
}
