package cz.kpsys.portaro.ext.wallet;

import cz.kpsys.portaro.commons.contextual.ContextualProvider;
import cz.kpsys.portaro.commons.object.Provider;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.user.Person;
import cz.kpsys.portaro.user.UserStringGenerator;
import cz.kpsys.portaro.user.role.reader.ReaderRole;
import de.brendamour.jpasskit.PKBarcode;
import de.brendamour.jpasskit.PKField;
import de.brendamour.jpasskit.PKPass;
import de.brendamour.jpasskit.enums.PKBarcodeFormat;
import de.brendamour.jpasskit.enums.PKPassType;
import de.brendamour.jpasskit.passes.PKGenericPass;
import de.brendamour.jpasskit.signing.*;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

import java.awt.*;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class AppleWalletCardCreator implements WalletCardCreator<byte[]> {

    @NonNull ContextualProvider<Department, @NonNull CardSettings> cardSettingsContextualProvider;
    @NonNull UserStringGenerator prettyUserNameGenerator;
    @NonNull Provider<@NonNull String> appleKeystorePassword;

    private static final String PASS_TYPE_IDENTIFIER = "pass.cz.systemist.verbis";
    private static final String TEAM_IDENTIFIER = "8MV88GCVFK";
    private static final String ORGANIZATION_NAME = "Systemist a.s.";
    private static final String KEYSTORE_PATH = "/applewallet/certs/keystore.p12";
    private static final String APPLE_WWDR_CERT_PATH = "/applewallet/certs/apple-wwdr-ca.cer";

    private static final String STRIP_ICON_PATH = "/applewallet/icon.png";
    private static final String STRIP_IMAGE_PATH = "/applewallet/strip.png";

    public byte[] generateWalletCard(Person person, Department ctx) throws Exception {
        CardSettings cardSettings = cardSettingsContextualProvider.getOn(ctx);

        if (!cardSettings.enabled()) {
            throw new RuntimeException("Apple Wallet card generation is not enabled");
        }

        ReaderRole readerRole = person.roleStreamOn(ReaderRole.class, ctx)
                .findFirst()
                .orElseThrow();

        String passId = UUID.randomUUID().toString();
        String barcodeValue = readerRole.getBarCode();

        List<PKField> auxiliaryFields = new ArrayList<>();

        if (readerRole.getRegistrationDate() != null) {
            auxiliaryFields.add(PKField.builder()
                    .key("registrationDate")
                    .value(readerRole.getRegistrationDate().format(LOCAL_DATE_FORMATTER))
                    .label("Datum registrace")
                    .build());
        }

        if (readerRole.getRegistrationExpirationDate().isPresent()) {
            auxiliaryFields.add(PKField.builder()
                    .key("expirationDate")
                    .value(readerRole.getRegistrationExpirationDate().get().format(LOCAL_DATE_FORMATTER))
                    .label("Datum expirace")
                    .build());
        }

        if (readerRole.getReaderCategory() != null) {
            auxiliaryFields.add(PKField.builder()
                    .key("category")
                    .value(readerRole.getReaderCategory().getName())
                    .label("Kategorie")
                    .build());
        }

        PKPass pass = PKPass.builder()
                .formatVersion(1)
                .passTypeIdentifier(PASS_TYPE_IDENTIFIER)
                .serialNumber(passId)
                .teamIdentifier(TEAM_IDENTIFIER)
                .organizationName(ORGANIZATION_NAME)
                .description("Kartička čtenáře testovací knihovny eVerbis")
                .logoText("Testovací knihovna eVerbis")
                .backgroundColor(Color.decode(cardSettings.backgroundColor()))
                .foregroundColor(Color.decode(cardSettings.textColor()))
                .labelColor(Color.decode(cardSettings.labelColor()))
                .pass(PKGenericPass.builder()
                        .passType(PKPassType.PKStoreCard)
                        .headerFields(List.of(
                                PKField.builder()
                                        .key("header")
                                        .value(barcodeValue)
                                        .label("Čárový kód")
                                        .build())
                        )
                        .primaryFields(List.of(
                                PKField.builder()
                                        .key("member")
                                        .value(prettyUserNameGenerator.generate(person))
                                        .label("Čtenář")
                                        .build())
                        )
                        .auxiliaryFields(auxiliaryFields)
                )
                .barcodes(List.of(
                                PKBarcode.builder()
                                        .format(PKBarcodeFormat.PKBarcodeFormatCode128)
                                        .message(barcodeValue)
                                        .messageEncoding(StandardCharsets.ISO_8859_1)
                                        .build()
                        )
                )
                .build();

        InputStream iconStream = getClass().getResourceAsStream(STRIP_ICON_PATH);
        InputStream stripStream = getClass().getResourceAsStream(STRIP_IMAGE_PATH);

        PKPassTemplateInMemory template = new PKPassTemplateInMemory();
        template.addFile("icon.png", iconStream);
        template.addFile("strip.png", stripStream);

        InputStream keystoreStream = getClass().getResourceAsStream(KEYSTORE_PATH);
        InputStream appleWwdrCaStream = getClass().getResourceAsStream(APPLE_WWDR_CERT_PATH);

        PKSigningInformation pkSigningInformation = new PKSigningInformationUtil().loadSigningInformationFromPKCS12AndIntermediateCertificate(keystoreStream, appleKeystorePassword.get(), appleWwdrCaStream);
        PKFileBasedSigningUtil pkSigningUtil = new PKFileBasedSigningUtil();

        return pkSigningUtil.createSignedAndZippedPkPassArchive(pass, template, pkSigningInformation);
    }
}
