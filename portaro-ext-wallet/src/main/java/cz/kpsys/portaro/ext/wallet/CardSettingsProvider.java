package cz.kpsys.portaro.ext.wallet;

import cz.kpsys.portaro.commons.contextual.ContextualProvider;
import cz.kpsys.portaro.department.Department;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class CardSettingsProvider implements ContextualProvider<Department, @NonNull CardSettings> {

    @NonNull ContextualProvider<Department, @NonNull Boolean> enabledProvider;
    @NonNull ContextualProvider<Department, @NonNull String> backgroundColorProvider;
    @NonNull ContextualProvider<Department, @NonNull String> textColorProvider;
    @NonNull ContextualProvider<Department, @NonNull String> labelColorProvider;

    public CardSettings getOn(Department ctx) {
        return new CardSettings(
                enabledProvider.getOn(ctx),
                backgroundColorProvider.getOn(ctx),
                textColorProvider.getOn(ctx),
                labelColorProvider.getOn(ctx)
        );
    }
}
