package cz.kpsys.portaro.ext.wallet;

import cz.kpsys.portaro.app.CatalogWebConstants;
import cz.kpsys.portaro.auth.current.ActiveUser;
import cz.kpsys.portaro.commons.contextual.ContextualProvider;
import cz.kpsys.portaro.department.CurrentDepartment;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.user.Person;
import cz.kpsys.portaro.user.User;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.*;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import java.net.URI;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
@Slf4j
@RequestMapping(CatalogWebConstants.API_URL_PREFIX + CatalogWebConstants.WALLET_URL_PART)
@ResponseBody
public class WalletCardApiController {

    @NonNull AppleWalletCardCreator appleWalletCardCreator;
    @NonNull GoogleWalletCardCreator googleWalletCardCreator;
    @NonNull ContextualProvider<Department, @NonNull CardSettings> cardSettingsContextualProvider;

    @RequestMapping("generate/apple")
    public ResponseEntity<byte[]> getAppleWalletCard(@ActiveUser User activeUser, @CurrentDepartment Department ctx) throws Exception {
        Person person = Person.requireUserIsPerson(activeUser);
        byte[] passData = appleWalletCardCreator.generateWalletCard(person, ctx);

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.valueOf("application/vnd.apple.pkpass"));
        headers.setContentDisposition(ContentDisposition.attachment().filename("VerbisLibraryCard.pkpass").build());

        return new ResponseEntity<>(passData, headers, HttpStatus.OK);
    }

    @RequestMapping("generate/google")
    public ResponseEntity<Void> getGooglePayCard(@ActiveUser User activeUser, @CurrentDepartment Department ctx) throws Exception {
        Person person = Person.requireUserIsPerson(activeUser);
        URI walletUrl = googleWalletCardCreator.generateWalletCard(person, ctx);

        if (walletUrl == null) {
            throw new IllegalStateException("Google Wallet card generation failed");
        }

        return ResponseEntity.status(HttpStatus.FOUND)
                .location(walletUrl)
                .build();
    }

    @RequestMapping("settings")
    public CardSettings getSettings(@CurrentDepartment Department ctx) {
        return cardSettingsContextualProvider.getOn(ctx);
    }
}