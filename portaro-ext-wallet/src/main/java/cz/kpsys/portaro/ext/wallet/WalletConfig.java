package cz.kpsys.portaro.ext.wallet;

import cz.kpsys.portaro.commons.contextual.ContextualProvider;
import cz.kpsys.portaro.commons.object.StaticProvider;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.setting.SettingLoader;
import cz.kpsys.portaro.user.UserStringGenerator;
import cz.kpsys.portaro.view.web.CatalogWebSettingsKeys;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.experimental.NonFinal;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class WalletConfig {

    @NonNull UserStringGenerator prettyUserNameGenerator;
    @NonNull SettingLoader settingLoader;

    @Value("${wallet.appleKeystorePassword}")
    @NonFinal
    String appleKeystorePassword;

    @Value("${wallet.googleServiceAccountPath}")
    @NonFinal
    String googleServiceAccountPath;

    @Value("${wallet.googleIssuerId}")
    @NonFinal
    String googleIssuerId;

    @Bean
    public ContextualProvider<Department, @NonNull CardSettings> cardSettingsContextualProvider() {
        return new CardSettingsProvider(
                settingLoader.getDepartmentedProvider(CatalogWebSettingsKeys.BARCODE_CARDS_ENABLED),
                settingLoader.getDepartmentedProvider(CatalogWebSettingsKeys.BARCODE_CARD_BACKGROUND_COLOR),
                settingLoader.getDepartmentedProvider(CatalogWebSettingsKeys.BARCODE_CARD_TEXT_COLOR),
                settingLoader.getDepartmentedProvider(CatalogWebSettingsKeys.BARCODE_CARD_LABEL_COLOR)
        );
    }

    @Bean
    public AppleWalletCardCreator appleWalletCardCreator() {
        return new AppleWalletCardCreator(
                cardSettingsContextualProvider(),
                prettyUserNameGenerator,
                StaticProvider.of(appleKeystorePassword)
        );
    }

    @Bean
    public GoogleWalletCardCreator googleWalletCardCreator() {
        return new GoogleWalletCardCreator(
                cardSettingsContextualProvider(),
                prettyUserNameGenerator
        );
    }

    @Bean
    public WalletCardApiController walletApiController() {
        return new WalletCardApiController(
                appleWalletCardCreator(),
                googleWalletCardCreator(),
                cardSettingsContextualProvider()
        );
    }
}
