package cz.kpsys.portaro.ext.wallet;

import com.auth0.jwt.JWT;
import com.auth0.jwt.algorithms.Algorithm;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.auth.oauth2.GoogleCredentials;
import com.google.auth.oauth2.ServiceAccountCredentials;
import cz.kpsys.portaro.commons.contextual.ContextualProvider;
import cz.kpsys.portaro.commons.object.Provider;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.user.Person;
import cz.kpsys.portaro.user.UserStringGenerator;
import cz.kpsys.portaro.user.role.reader.ReaderRole;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

import java.io.InputStream;
import java.net.URI;
import java.security.interfaces.RSAPrivateKey;
import java.util.*;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class GoogleWalletCardCreator implements WalletCardCreator<URI> {

    @NonNull ContextualProvider<Department, @NonNull CardSettings> cardSettingsContextualProvider;
    @NonNull UserStringGenerator prettyUserNameGenerator;
    @NonNull Provider<@NonNull String> googleServiceAccountPath;
    @NonNull Provider<@NonNull String> googleIssuerId;

    private static final String CLASS_SUFFIX = "library_card";
    private static final String LOGO_IMAGE_PATH = "/googlewallet/logo.png";

    private static final ObjectMapper objectMapper = new ObjectMapper();

    @Override
    public URI generateWalletCard(Person person, Department ctx) throws Exception {
        CardSettings cardSettings = cardSettingsContextualProvider.getOn(ctx);

        if (!cardSettings.enabled()) {
            throw new RuntimeException("Google Wallet card generation is not enabled");
        }

        ReaderRole readerRole = person.roleStreamOn(ReaderRole.class, ctx)
                .findFirst()
                .orElseThrow();

        String passId = UUID.randomUUID().toString();
        String barcodeValue = readerRole.getBarCode();

        // Load service account credentials
        GoogleCredentials credentials = GoogleCredentials.fromStream(new FileInputStream(SERVICE_ACCOUNT_FILE));

        if (!(credentials instanceof ServiceAccountCredentials serviceAccountCredentials)) {
            throw new IllegalStateException("Credentials are not a service account");
        }

        RSAPrivateKey privateKey = (RSAPrivateKey) serviceAccountCredentials.getPrivateKey();
        String serviceAccountEmail = serviceAccountCredentials.getClientEmail();

        // Define the pass object
        Map<String, Object> passObject = new LinkedHashMap<>();
        passObject.put("id", passId);
        passObject.put("classId", ISSUER_ID + "." + CLASS_SUFFIX);
        passObject.put("state", "ACTIVE");

        /* TODO: Put image here
        passObject.put("heroImage", Map.of(
                "sourceUri", Map.of("uri", user.getClientLogoUrl())
        ));
        */

        passObject.put("textModulesData", List.of(
                Map.of("header", "Library Member", "body", prettyUserNameGenerator.generate(person))
        ));

        passObject.put("barcode", Map.of(
                "type", "CODE_128",
                "value", barcodeValue,
                "alternateText", "Verbis Entry"
        ));

        // JWT payload
        Map<String, Object> payload = new LinkedHashMap<>();
        payload.put("iss", serviceAccountEmail);
        payload.put("aud", "google");
        payload.put("typ", "savetowallet");
        payload.put("payload", Map.of("genericObjects", List.of(passObject)));

        // Sign JWT
        Algorithm algorithm = Algorithm.RSA256(null, privateKey);
        String token = JWT.create()
                .withClaim("iss", (String) payload.get("iss"))
                .withClaim("aud", (String) payload.get("aud"))
                .withClaim("typ", (String) payload.get("typ"))
                .withClaim("payload", objectMapper.convertValue(payload.get("payload"), Map.class))
                .sign(algorithm);

        // Return Add to Google Wallet URL
        return URI.create("https://pay.google.com/gp/v/save/" + token);
    }
}
