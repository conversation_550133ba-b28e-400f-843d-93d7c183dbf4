package cz.kpsys.portaro.config;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.module.SimpleModule;
import com.fasterxml.jackson.databind.ser.impl.SimpleFilterProvider;
import cz.kpsys.portaro.ExternalLogFields;
import cz.kpsys.portaro.FieldsNotResolvableException;
import cz.kpsys.portaro.action.ActionSaver;
import cz.kpsys.portaro.action.SpringDbActionSaver;
import cz.kpsys.portaro.alert.Alert;
import cz.kpsys.portaro.alert.ExceptionedAlert;
import cz.kpsys.portaro.app.CatalogWebConstants;
import cz.kpsys.portaro.app.ssl.HttpsKeysSource;
import cz.kpsys.portaro.appserver.*;
import cz.kpsys.portaro.appserver.dml.DmlAppserverService;
import cz.kpsys.portaro.appserver.dml.MappingAppserverDmlAppserverService;
import cz.kpsys.portaro.appserver.mapping.BasicMappingAppserverService;
import cz.kpsys.portaro.appserver.mapping.MappingAppserverService;
import cz.kpsys.portaro.auth.AuthenticationHolder;
import cz.kpsys.portaro.auth.BadLoginCredentialsException;
import cz.kpsys.portaro.auth.InsufficientlyAuthenticatedException;
import cz.kpsys.portaro.auth.bankid.BankIDAuthorizationCodeAuthSettingKeys;
import cz.kpsys.portaro.auth.cas.CasAuthSettingKeys;
import cz.kpsys.portaro.auth.cas.CasTicketValidatorProtocol;
import cz.kpsys.portaro.auth.credreg.CredentialsRegistrationSettingKeys;
import cz.kpsys.portaro.auth.current.CurrentAuthWebResolver;
import cz.kpsys.portaro.auth.current.CurrentAuthWebResolverByHolder;
import cz.kpsys.portaro.auth.current.resolver.CompositeUserAuthenticationAuthenticationResolver;
import cz.kpsys.portaro.auth.external.ExternalAuthenticationCancelException;
import cz.kpsys.portaro.auth.ezak.EzakAuthSettingKeys;
import cz.kpsys.portaro.auth.internal.InternalLoginCredentialsProperties;
import cz.kpsys.portaro.auth.ldap.LdapAuthSettingKeys;
import cz.kpsys.portaro.auth.mojeid.MojeIDAuthorizationCodeAuthSettingKeys;
import cz.kpsys.portaro.auth.multifactor.MultifactorAuthSettingKeys;
import cz.kpsys.portaro.auth.oauth2.authorizationcode.OAuth2AuthorizationCodeAuthSettingKeys;
import cz.kpsys.portaro.auth.pairing.PairingSettingKeys;
import cz.kpsys.portaro.auth.saml2.idp.Saml2IdentityProviderSettingKeys;
import cz.kpsys.portaro.auth.saml2.sp.Saml2ServiceProviderSettingKeys;
import cz.kpsys.portaro.auth.sidechannel.SideChannelAuthSettingKeys;
import cz.kpsys.portaro.bool.StringToBooleanConverter;
import cz.kpsys.portaro.commons.barcode.*;
import cz.kpsys.portaro.commons.cache.CacheService;
import cz.kpsys.portaro.commons.contextual.*;
import cz.kpsys.portaro.commons.convert.*;
import cz.kpsys.portaro.commons.crypto.MessageCryptor;
import cz.kpsys.portaro.commons.date.*;
import cz.kpsys.portaro.commons.io.BytesRange;
import cz.kpsys.portaro.commons.io.FileZipperImpl;
import cz.kpsys.portaro.commons.io.StringToFileConverter;
import cz.kpsys.portaro.commons.ip.*;
import cz.kpsys.portaro.commons.json.ByIntConverterJsonDeserializer;
import cz.kpsys.portaro.commons.json.ByStringConverterJsonDeserializer;
import cz.kpsys.portaro.commons.json.ConverterToStringJsonSerializer;
import cz.kpsys.portaro.commons.licence.FeatureNotEnabledException;
import cz.kpsys.portaro.commons.localization.*;
import cz.kpsys.portaro.commons.logging.Logged;
import cz.kpsys.portaro.commons.mail.*;
import cz.kpsys.portaro.commons.object.*;
import cz.kpsys.portaro.commons.object.repo.*;
import cz.kpsys.portaro.commons.util.ListUtil;
import cz.kpsys.portaro.commons.util.StringUtil;
import cz.kpsys.portaro.commons.validation.nullablenotblank.NullableNotBlank;
import cz.kpsys.portaro.commons.web.*;
import cz.kpsys.portaro.conversation.ActionRequestUrlGenerator;
import cz.kpsys.portaro.databasebackup.DbBackupSettingKeys;
import cz.kpsys.portaro.department.*;
import cz.kpsys.portaro.exemplar.ExemplarConstants;
import cz.kpsys.portaro.exemplar.ExemplarSettingKeys;
import cz.kpsys.portaro.ext.alive.AliveSettingKeys;
import cz.kpsys.portaro.ext.bakalari.BakalariSettingKeys;
import cz.kpsys.portaro.ext.cpk.CpkSettingKeys;
import cz.kpsys.portaro.ext.edookit.EdookitSettingKeys;
import cz.kpsys.portaro.ext.edupage.EdupageSettingKeys;
import cz.kpsys.portaro.ext.ifis.IfisSettingKeys;
import cz.kpsys.portaro.ext.obalkyknih.ObalkyknihSettingKeys;
import cz.kpsys.portaro.ext.powerkey.PowerkeySettingKeys;
import cz.kpsys.portaro.ext.report.server.ReportServerSettingsKeys;
import cz.kpsys.portaro.ext.sol.SolSettingKeys;
import cz.kpsys.portaro.ext.sutin.SutinSettingKeys;
import cz.kpsys.portaro.ext.unis.UnisSettingKeys;
import cz.kpsys.portaro.ext.unob.UnobSettingKeys;
import cz.kpsys.portaro.ext.ziskej.ZiskejSettingKeys;
import cz.kpsys.portaro.file.FileSettingKeys;
import cz.kpsys.portaro.form.spring.ConstraintViolationTextResolver;
import cz.kpsys.portaro.form.valueeditor.bool.BooleanValueEditorType;
import cz.kpsys.portaro.formannotation.annotations.valueeditor.ValueEditorType;
import cz.kpsys.portaro.formannotation.annotations.valueeditor.date.DateValueEditorGranularity;
import cz.kpsys.portaro.formannotation.annotations.valueeditor.text.TextValueEditorType;
import cz.kpsys.portaro.layout.json.GenericLayoutSettingHolder;
import cz.kpsys.portaro.loan.LoanSettingKeys;
import cz.kpsys.portaro.loan.ill.IllSettingKeys;
import cz.kpsys.portaro.loan.ill.LibraryNameGenerationMode;
import cz.kpsys.portaro.loan.ill.LibraryNameSiglaInsertionMode;
import cz.kpsys.portaro.localization.*;
import cz.kpsys.portaro.ncip.NcipSettingKeys;
import cz.kpsys.portaro.oai.provider.impl.OaiSettingKeys;
import cz.kpsys.portaro.opening.StringToTimeRangeConverter;
import cz.kpsys.portaro.payment.PaymentSettingKeys;
import cz.kpsys.portaro.platform.OsCheck;
import cz.kpsys.portaro.platform.PlatformUtils;
import cz.kpsys.portaro.pops.agreement.AgreementStyle;
import cz.kpsys.portaro.record.RecordSettingKeys;
import cz.kpsys.portaro.record.comment.CommentStyle;
import cz.kpsys.portaro.record.export.RecordExportSettingKeys;
import cz.kpsys.portaro.record.link.SearchElsewhereItem;
import cz.kpsys.portaro.record.view.StringToFieldTypeFilterConverter;
import cz.kpsys.portaro.record.view.StringToIntegerFieldTypeFilterMapConverter;
import cz.kpsys.portaro.search.config.SearchLoaderBuilderFactory;
import cz.kpsys.portaro.search.sorting.StringToSortOrderConverter;
import cz.kpsys.portaro.security.AccessDeniedException;
import cz.kpsys.portaro.security.NotLoggedInException;
import cz.kpsys.portaro.security.PermissionRegistry;
import cz.kpsys.portaro.security.SecurityManager;
import cz.kpsys.portaro.security.appserver.UserAddingAppserverService;
import cz.kpsys.portaro.setting.*;
import cz.kpsys.portaro.shutdown.*;
import cz.kpsys.portaro.sip2.server.impl.Sip2ServerSettingKeys;
import cz.kpsys.portaro.sorting.SortingItem;
import cz.kpsys.portaro.sql.generator.QueryFactory;
import cz.kpsys.portaro.time.StringToDurationConverter;
import cz.kpsys.portaro.user.JsonOnlyForRolePropertyFilter;
import cz.kpsys.portaro.user.ReaderAccountSettingKeys;
import cz.kpsys.portaro.user.UserSettingKeys;
import cz.kpsys.portaro.user.edit.form.IllegalFormSettingsException;
import cz.kpsys.portaro.user.payment.provider.PaymentProviderSettingKeys;
import cz.kpsys.portaro.user.payment.provider.csobgw.CsobGwSettingKeys;
import cz.kpsys.portaro.user.payment.provider.manual.ManualPaymentProviderSettingKeys;
import cz.kpsys.portaro.user.registration.RegistrationSettingKeys;
import cz.kpsys.portaro.user.role.reader.PrintType;
import cz.kpsys.portaro.user.sec.PermissionRegistrySecurityManager;
import cz.kpsys.portaro.verbisboxer.manager.VerbisboxerManagerApiSettingKeys;
import cz.kpsys.portaro.view.web.CatalogWebSettingsKeys;
import cz.kpsys.portaro.view.web.currentdepartment.*;
import cz.kpsys.portaro.view.web.ratelimit.RateLimitExceededException;
import cz.kpsys.portaro.view.web.rest.*;
import cz.kpsys.portaro.web.exception.CompositeExceptionHttpStatusResolver;
import cz.kpsys.portaro.web.locale.*;
import cz.kpsys.portaro.web.server.ServerUrlConfiguration;
import cz.kpsys.portaro.web.server.ServerUrlToPublicContextPathConverter;
import jakarta.persistence.EntityManager;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.ConstraintViolationException;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.experimental.NonFinal;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.context.MessageSource;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Scope;
import org.springframework.context.annotation.ScopedProxyMode;
import org.springframework.context.event.EventListener;
import org.springframework.core.convert.ConversionFailedException;
import org.springframework.core.convert.converter.Converter;
import org.springframework.dao.EmptyResultDataAccessException;
import org.springframework.format.support.DefaultFormattingConversionService;
import org.springframework.format.support.FormattingConversionService;
import org.springframework.http.HttpStatus;
import org.springframework.jdbc.BadSqlGrammarException;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcOperations;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.security.authentication.InsufficientAuthenticationException;
import org.springframework.security.authentication.event.InteractiveAuthenticationSuccessEvent;
import org.springframework.web.HttpRequestMethodNotSupportedException;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.client.ResourceAccessException;
import org.springframework.web.context.WebApplicationContext;
import org.springframework.web.method.annotation.MethodArgumentTypeMismatchException;
import org.springframework.web.servlet.LocaleResolver;

import java.io.File;
import java.text.MessageFormat;
import java.text.NumberFormat;
import java.time.Duration;
import java.time.Instant;
import java.time.LocalDate;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

import static cz.kpsys.portaro.CoreConstants.Locales;
import static cz.kpsys.portaro.app.CatalogConstants.CURRENCY_CZK_LOCALE;
import static cz.kpsys.portaro.commons.convert.StringToStringListConverter.DEFAULT_LIST_DELIMITERS;
import static cz.kpsys.portaro.config.SettingKeys.*;
import static cz.kpsys.portaro.record.RecordSettingKeys.HOLDING_ASSIGNABLE_FROM_CONTEXTS;
import static cz.kpsys.portaro.record.RecordSettingKeys.NEWEST_SEARCH_VIEW_NAME;
import static cz.kpsys.portaro.setting.CoreSettingKeys.DEFAULT_LOCALE;
import static cz.kpsys.portaro.setting.CoreSettingKeys.HTML_FRAME_EMBEDDING_ALLOWED;
import static cz.kpsys.portaro.shutdown.RestartingPolicy.*;

// @formatter:off
@Configuration
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
@Slf4j
public class CoreConfig {

    /**
     * root department id
     */
    @Value("${portaro.rootDepartment}")
    @NonFinal int rootDepartment;

    @NonNull Provider<@NonNull Integer> realRootDepartmentIdProvider;
    @NonNull CacheService cacheService;
    @NonNull QueryFactory queryFactory;
    @NonNull NamedParameterJdbcOperations jdbcTemplate;
    @NonNull AllBySectionsLoader<CustomSetting<String>> envVariablesAwareCustomSettingLoader;
    @NonNull AllBySectionsLoader<SettingTypeDto<String>> settingTypeLoader;
    @NonNull AppserverService authorizedAppserverService;
    @NonNull EntityManager entityManager;
    @NonNull CompositeAddableAllValuesProvider<Alert> globalAlertsProvider;
    @NonNull ConfigurableApplicationContext context;
    @NonNull ExternalLogFields externalLogFields;
    @NonNull HttpServletRequest httpServletRequest;
    @NonNull ObjectMapper objectMapper;
    @NonNull ApplicationEventPublisher applicationEventPublisher;
    @NonNull CodebookLoaderBuilderFactory codebookLoaderBuilderFactory;



    @EventListener(ApplicationReadyEvent.class)
    public void registerExternalLogField() {
        externalLogFields.setStartedPhase();
        externalLogFields.setPortaroUrl(rootServerUrlProvider().get());
        externalLogFields.setSerialCode(settingLoader().getOnRootProvider(SERIAL_CODE).get());
        externalLogFields.setLibraryName(settingLoader().getOnRootProvider(SettingKeys.LIBRARY_NAME).get());
        externalLogFields.setLibraryCity(settingLoader().getOnRootProvider(SettingKeys.LIBRARY_CITY).get());
    }


    @Logged
    @Scheduled(cron = "0 20 4 * * *") //kazdy den ve 4:20 rano
    public void restartOrRefreshSystem() {
        RestartingPolicy when;
        try {
            when = settingLoader().getOnRoot(PERIODIAL_RESTARTING);
        } catch (Exception e) {
            log.error("Cannot resolve periodical restarting policy, fallback to {}", ALWAYS_IF_WINDOWS, e);
            when = ALWAYS_IF_WINDOWS;
        }
        if (when == ALWAYS || (when == ALWAYS_IF_WINDOWS && PlatformUtils.getOperatingSystemType() == OsCheck.OSType.WINDOWS)) {
            applicationRestarter().restart();
        } else {
            cacheService.invalidateAllCaches();
        }
    }


    @Bean(destroyMethod = "")
    public ApplicationShutdowner applicationShutdowner() {
        return new ApplicationShutdownerByContextInterruptAndSystemExit(context);
    }

    @Bean
    public ApplicationRestarter applicationRestarter() {
        return new ApplicationRestarterByShutdown(applicationShutdowner());
    }

    @Bean
    public String contextPath() {
        return "";
    }

    @Bean
    public UserFriendlyExceptionTextResolver userFriendlyExceptionTextResolver() {
        CompositeUserFriendlyExceptionTextResolver resolver = new CompositeUserFriendlyExceptionTextResolver();
        return resolver
                .with(EmptyResultDataAccessException.class, e -> Texts.ofMessageCoded(LocalizationCodes.NotFound))
                .with(org.springframework.security.access.AccessDeniedException.class, e -> Texts.ofMessageCoded(LocalizationCodes.ACCESS_DENIED))
                .with(UserFriendlyException.class, Labeled::getText)
                .with(ItemIsInUseModifyException.class, e -> Texts.ofMessageCoded(LocalizationCodes.CannotWriteItemIsUsed))
                .with(DuplicateValueInsertModifyException.class, e -> Texts.ofMessageCoded("commons.edit.SomeValueIsNotUnique"))
                .with(BadSqlGrammarException.class, e -> Texts.ofMessageCoded(LocalizationCodes.DataLoadError)) //dont write SQL to user!
                .with(AppserverAccessException.class, e -> Texts.ofMessageCoded(LocalizationCodes.DataAccessError))
                .with(AppserverResponseTimeoutException.class, e -> Texts.ofMessageCoded(LocalizationCodes.DataAccessError))
                .with(ResourceAccessException.class, e -> Texts.ofMessageCoded(LocalizationCodes.DataAccessError))
                .with(AppserverResponseParsingException.class, e -> Texts.ofMessageCoded(LocalizationCodes.DataLoadError))
                .with(AppserverTransferException.class, e -> Texts.ofMessageCoded(LocalizationCodes.DataLoadError))
                .with(IllegalFormSettingsException.class, e -> Texts.ofArgumentedMessageCoded(LocalizationCodes.ILLEGAL_FORM_SETTINGS, e.getMessage()))
                .with(ConstraintViolationException.class, new ConstraintViolationTextResolver())
                .withUnwrap(MethodArgumentTypeMismatchException.class)
                .withUnwrap(ConversionFailedException.class);
    }

    @Bean
    public CompositeExceptionHttpStatusResolver exceptionHttpStatusResolver() {
        return new CompositeExceptionHttpStatusResolver()
                .with(NotLoggedInException.class, HttpStatus.UNAUTHORIZED)
                .with(InsufficientlyAuthenticatedException.class, HttpStatus.FORBIDDEN)
                .with(InsufficientAuthenticationException.class, HttpStatus.FORBIDDEN)
                .with(ExternalAuthenticationCancelException.class, HttpStatus.BAD_REQUEST)
                .with(AccessDeniedException.class, HttpStatus.FORBIDDEN)
                .with(org.springframework.security.access.AccessDeniedException.class, HttpStatus.FORBIDDEN)
                .with(FieldsNotResolvableException.class, HttpStatus.BAD_REQUEST)
                .with(MethodArgumentNotValidException.class, HttpStatus.BAD_REQUEST)
                .with(ConstraintViolationException.class, HttpStatus.BAD_REQUEST)
                .with(BadLoginCredentialsException.class, HttpStatus.BAD_REQUEST)
                .with(EmptyResultDataAccessException.class, HttpStatus.NOT_FOUND)
                .with(ItemNotFoundException.class, HttpStatus.NOT_FOUND)
                .with(MethodArgumentTypeMismatchException.class, HttpStatus.NOT_FOUND)
                .with(HttpRequestMethodNotSupportedException.class, HttpStatus.NOT_FOUND)
                .with(RateLimitExceededException.class, HttpStatus.TOO_MANY_REQUESTS)
                .with(FeatureNotEnabledException.class, HttpStatus.FORBIDDEN)
                .with(HttpBadRequestException.class, HttpStatus.BAD_REQUEST)
                .with(HttpRangeNotSatisfiableException.class, HttpStatus.REQUESTED_RANGE_NOT_SATISFIABLE);
    }


    @Bean
    public LocalizationsApiController localizationsApiController() {
        return new LocalizationsApiController(
                localizationLoader(),
                localizationsSaver(),
                settingLoader().getDepartmentedProvider(SettingKeys.ENABLED_LOCALES),
                localizationRequestToLocalizationConverter(),
                localizationToLocalizationResponseConverter(),
                stringToLocaleConverter(),
                securityManager());
    }

    @Bean
    public ContextualProvider<Department, List<String>> localizationCreationRequestPropsProvider() {
        return settingLoader().getDepartmentedProvider(SettingKeys.ENABLED_LOCALES).andThen(enabledLocales -> enabledLocales.stream().map(Locale::getLanguage).toList());
    }

    @Bean
    public FallbackLocaleResolver fallbackLocaleResolver() {
        return new FallbackLocaleResolver()
                .add(Locales.CS, Locales.SK, Locales.EN)
                .add(Locales.SK, Locales.CS, Locales.EN)
                .addDefaultFallback(StaticProvider.of(Locales.EN));
    }

    @Bean
    public ContextualFunction<LocalizationRequest, Department, Localization> localizationRequestToLocalizationConverter() {
        return new LocalizationRequestToLocalizationConverter<>(settingLoader().getDepartmentedProvider(SettingKeys.ENABLED_LOCALES));
    }

    @Bean
    public Converter<Localization, LocalizationResponse> localizationToLocalizationResponseConverter() {
        return new LocalizationToLocalizationResponse();
    }

    @Bean
    public LocaleResolver portaroLocaleResolver() {
        LocaleToStringConverter localeToStringConverter = new LocaleToStringConverter();
        StringToLocaleConverter stringToLocaleConverter = new StringToLocaleConverter();
        return new CompositeLocaleResolver()
                .with(new RequestAttributeOptionalLocaleResolver())
                .with(new RequestUrlParameterOptionalLocaleResolver(CatalogWebConstants.LOCALE_URL_PARAMETER_NAME, stringToLocaleConverter))
                .with(new CookieOptionalLocaleResolver(CatalogWebConstants.LOCALE_COOKIE_NAME, stringToLocaleConverter, localeToStringConverter))
                .with(new RequestHeaderOptionalLocaleResolver<>(currentDepartmentWebResolver(), settingLoader().getDepartmentedProvider(SettingKeys.ENABLED_LOCALES), fallbackLocaleResolver()))
                .with(new ProvidedOptionalLocaleResolver<>(currentDepartmentWebResolver(), settingLoader().getDepartmentedProvider(DEFAULT_LOCALE)))
                .withSetter(new CookieOptionalLocaleResolver(CatalogWebConstants.LOCALE_COOKIE_NAME, stringToLocaleConverter, localeToStringConverter));
    }

    @Bean
    public WebResolver<Locale> localeWebResolver() {
        return request -> Optional.of(portaroLocaleResolver().resolveLocale(request));
    }

    @Bean
    public FormattingConversionService conversionService() {
        DefaultFormattingConversionService bean = new DefaultFormattingConversionService();

        IdToObjectConverter<Integer, Department> integerToDepartmentConverter = new IdToObjectConverter<>(departmentLoader());
        IdToObjectConverter<Integer, PrintType> integerToPrintTypeConverter = new IdToObjectConverter<>(PrintType.CODEBOOK);
        IdToObjectConverter<Integer, TimeGranularity> integerToTimeGranularityConverter = new IdToObjectConverter<>(TimeGranularity.CODEBOOK);

        bean.addConverter(Integer.class, Department.class, integerToDepartmentConverter);
        bean.addConverter(Integer.class, PrintType.class, integerToPrintTypeConverter);
        bean.addConverter(Integer.class, TimeGranularity.class, integerToTimeGranularityConverter);

        bean.addConverter(String.class, Boolean.class, StringToBooleanConverter.returningNullWhenNullSource());
        bean.addConverter(String.class, File.class, new StringToFileConverter());
        bean.addConverter(String.class, PrintType.class, StringToIntegerToAnyConverter.nullConvertingToNull(integerToPrintTypeConverter));
        bean.addConverter(String.class, Department.class, StringToIntegerToAnyConverter.nullConvertingToNull(integerToDepartmentConverter));
        bean.addConverter(String.class, TimeGranularity.class, StringToIntegerToAnyConverter.nullConvertingToNull(integerToTimeGranularityConverter));
        bean.addConverter(String.class, Date.class, new IsoStringToDateConverter());
        bean.addConverter(String.class, LocalDate.class, new IsoStringToLocalDateConverter());
        bean.addConverter(String.class, Instant.class, new IsoStringToInstantConverter());
        bean.addConverter(Date.class, String.class, new DateToIsoStringConverter());
        bean.addConverter(DateRange.class, String.class, new DateRangeToBase64StringConverter(objectMapper));

        return bean;
    }

    @Bean
    @Scope(proxyMode = ScopedProxyMode.INTERFACES)
    public ConverterRegisterer converterRegisterer() {
        return new MvcAndJsonConverterRegisterer(conversionService(), objectMapperModule(), objectMapper);
    }

    @Bean
    public SimpleModule objectMapperModule() {
        SimpleModule objectMapperModule = new SimpleModule("GlobalMapperModule");
        objectMapperModule
                .addSerializer(Text.class, new LocalizingTextJsonSerializer<>(translator(), localeWebResolver(), currentDepartmentWebResolver(), httpServletRequest))
                .addSerializer(TextValueEditorType.class, new ConverterToStringJsonSerializer<>(TextValueEditorType.class, TextValueEditorType.TO_JSON_CONVERTER))
                .addSerializer(DateValueEditorGranularity.class, new ConverterToStringJsonSerializer<>(DateValueEditorGranularity.class, DateValueEditorGranularity.TO_JSON_CONVERTER))
                .addSerializer(BooleanValueEditorType.class, new ConverterToStringJsonSerializer<>(BooleanValueEditorType.class, BooleanValueEditorType.TO_JSON_CONVERTER))
                .addSerializer(ValueEditorType.class, new ConverterToStringJsonSerializer<>(ValueEditorType.class, ValueEditorType.TO_JSON_CONVERTER))

                .addDeserializer(File.class, new ByStringConverterJsonDeserializer<>(File.class, new StringToFileConverter()))
                .addDeserializer(Text.class, new LocalizedTextDeserializer())
                .addDeserializer(TimeGranularity.class, new ByIntConverterJsonDeserializer<>(TimeGranularity.class, new IdToObjectConverter<>(TimeGranularity.CODEBOOK)))
                .addDeserializer(Department.class, new ByIntConverterJsonDeserializer<>(Department.class, new IdToObjectConverter<>(departmentLoader())))
                .addDeserializer(PrintType.class, new ByIntConverterJsonDeserializer<>(PrintType.class, new IdToObjectConverter<>(PrintType.CODEBOOK)));
        objectMapper.registerModule(objectMapperModule);
        return objectMapperModule;
    }

    @Bean
    public CompositeUserAuthenticationAuthenticationResolver compositeUserAuthenticationAuthenticationResolver() {
        return new CompositeUserAuthenticationAuthenticationResolver();
    }

    @Bean
    public AuthenticationHolder authenticationHolder() {
        AuthenticationHolder authenticationHolder = new AuthenticationHolder(compositeUserAuthenticationAuthenticationResolver());
        objectMapper.setFilterProvider(new SimpleFilterProvider()
                .addFilter("jsonOnlyForRoleFilter", new JsonOnlyForRolePropertyFilter(authenticationHolder)));
        authenticationHolder.addOnEvidedSuccessListener(authentication -> applicationEventPublisher.publishEvent(new InteractiveAuthenticationSuccessEvent(authentication, AuthenticationHolder.class)));
        return authenticationHolder;
    }

    @Bean
    public CurrentAuthWebResolver currentAuthWebResolver() {
        return new CurrentAuthWebResolverByHolder(authenticationHolder());
    }


    @Bean
    public Codebook<Department, Integer> departmentLoader() {
        Codebook<Department, Integer> baseCodebook = codebookLoaderBuilderFactory.create()
                .providedBy(new SpringDbDepartmentLoader(jdbcTemplate, queryFactory))
                .staticCached(Department.class.getSimpleName())
                .build();

        return FilteringCodebook.filteredGetAllOnly(baseCodebook,
                department -> department.getDeletionEventId() == null);
    }


    @Bean
    public DepartmentAccessor departmentAccessor() {
        return new DepartmentAccessorImpl(
                departmentLoader(),
                hierachicalDepartmentLoader()
        );
    }

    @Bean
    public HierarchicalDepartmentLoaderByDepartmentLoader hierachicalDepartmentLoader() {
        return new HierarchicalDepartmentLoaderByDepartmentLoader(departmentLoader());
    }

    @Bean
    public Provider<@NonNull Integer> rootDepartmentIdProvider() {
        if (rootDepartment != -1) {
            return StaticProvider.of(rootDepartment);
        }
        return realRootDepartmentIdProvider;
    }

    @Bean
    public Provider<@NonNull Department> rootDepartmentProvider() {
        return ProviderByIdProvider.of(departmentLoader(), rootDepartmentIdProvider())
                .withDefaultSupport(realRootDepartmentProvider(), -1);
    }

    @Bean
    public Provider<@NonNull Department> realRootDepartmentProvider() {
        return new RealRootDepartmentProvider(departmentLoader());
    }

    @Bean
    public Provider<@NullableNotBlank String> rootServerUrlProvider() {
        return settingLoader().getOnRootProvider(CoreSettingKeys.SERVER_URL);
    }

    @Bean
    public ContextualProvider<Department, @NullableNotBlank String> serverUrlProvider() {
        return settingLoader().getDepartmentedProvider(CoreSettingKeys.SERVER_URL);
    }

    @Bean
    public Provider<@NonNull String> rootSerialCodeProvider() {
        return settingLoader().getOnRootProvider(SERIAL_CODE);
    }

    @Bean
    public ContextualProvider<Department, @NonNull String> serialCodeProvider() {
        return settingLoader().getDepartmentedProvider(SERIAL_CODE);
    }

    @Bean
    public SettingLoader settingLoader() {
        DefaultSettingsRepoToCacheLoader loader = new DefaultSettingsRepoToCacheLoader(departmentLoader(), realRootDepartmentProvider(), settingTypeLoader, envVariablesAwareCustomSettingLoader);
        SettingRepo settingValueResolver = new SettingRepo(departmentLoader(), loader);
        DefaultSettingLoader bean = new DefaultSettingLoader(rootDepartmentProvider(), settingValueResolver);
        globalAlertsProvider.withProvider(() -> ListUtil.convert(loader.getLastReloadExceptions(), e -> new ExceptionedAlert(e.getText(), Alert.SEVERRENITY_DANGER, e)));

        cacheService.registerCleaner(bean);

        bean.registerKeyReturningNullWhenNullSource(CoreSettingKeys.SERVER_URL, new EToEConverter<>());
        bean.registerKeyThrowingWhenNullSource(PERIODIAL_RESTARTING, new ConversionTableConverter<>(INI_CONVERSION_TABLE));
        bean.registerKeyThrowingWhenNullSource(FORCE_HTTPS_ENABLED, StringToBooleanConverter.notAllowingNullSource());
        bean.registerKeyThrowingWhenNullSource(SERVER_REVERSE_PROXY, StringToBooleanConverter.notAllowingNullSource());
        bean.registerKeyReturningNullWhenNullSource(SERVER_LISTEN_IP, new StringToIpAddressConverter());
        bean.registerKeyThrowingWhenNullSource(HTML_FRAME_EMBEDDING_ALLOWED, StringToBooleanConverter.notAllowingNullSource());
        bean.registerKeyReturningValueWhenNullSource(INTERNAL_IP_ADDRESS_RANGE, new StringToRozsahIpAdresConverter(), EmptyIpAddressRange::new);
        bean.registerKeyReturningValueWhenNullSource(INTERNAL_IP_ADDRESSES, StringToAnyListConverter.create(new StringToIpAddressConverter()), List::of);
        bean.registerKeyReturningValueWhenNullSource(EXTERNAL_IP_ADDRESSES, StringToAnyListConverter.create(new StringToIpAddressConverter()), List::of);
        bean.registerKeyThrowingWhenNullSource(ACME_CERTIFICATE_LOCATION, new EToEConverter<>());
        bean.registerKeyThrowingWhenNullSource(ACME_PRIVATE_KEY_LOCATION, new EToEConverter<>());
        bean.registerKeyReturningNullWhenNullSource(ACME_CHALLENGE_KEY, new EToEConverter<>());
        bean.registerKeyThrowingWhenNullSource(INTERNAL_HTTPS_ENABLED, StringToBooleanConverter.notAllowingNullSource());
        bean.registerKeyReturningNullWhenNullSource(INTERNAL_HTTPS_PORT, new StringToIntegerConverter());
        bean.registerKeyThrowingWhenNullSource(INTERNAL_HTTPS_KEYS_SOURCE, new ConversionTableConverter<>(HttpsKeysSource.CONVERSION_TABLE));
        bean.registerKeyReturningNullWhenNullSource(INTERNAL_HTTPS_CERTIFICATE_LOCATION, new EToEConverter<>());
        bean.registerKeyReturningNullWhenNullSource(INTERNAL_HTTPS_CA_CERTIFICATE_LOCATION, new EToEConverter<>());
        bean.registerKeyReturningNullWhenNullSource(INTERNAL_HTTPS_PRIVATE_KEY_LOCATION, new EToEConverter<>());
        bean.registerKeyReturningNullWhenNullSource(INTERNAL_HTTPS_PRIVATE_KEY_PASSWORD, new EToEConverter<>());
        bean.registerKeyReturningNullWhenNullSource(INTERNAL_HTTPS_KEYSTORE_LOCATION, new EToEConverter<>());
        bean.registerKeyReturningNullWhenNullSource(INTERNAL_HTTPS_KEYSTORE_PASSWORD, new EToEConverter<>());
        bean.registerKeyReturningNullWhenNullSource(INTERNAL_HTTPS_KEYSTORE_KEY_ALIAS, new EToEConverter<>());

        bean.registerKeyThrowingWhenNullSource(ENABLED_LOCALES, StringToAnyListConverter.create(new StringToLocaleConverter()));
        bean.registerKeyReturningValueWhenNullSource(FORBIDDEN_MENU_ITEMS, new StringToStringListConverter(), List::of);
        bean.registerKeyReturningValueWhenNullSource(BUILT_IN_COVERS_BY_FONDS, new StringToIntegerAnyMapConverter<>(new EToEConverter<>()), Map::of);
        bean.registerKeyThrowingWhenNullSource(CAMERA_SCANNER_ENABLED, StringToBooleanConverter.notAllowingNullSource());
        bean.registerKeyThrowingWhenNullSource(APPSERVER_ENABLED, StringToBooleanConverter.notAllowingNullSource());

        bean.registerKeyThrowingWhenNullSource(SERIAL_CODE, new EToEConverter<>());
        bean.registerKeyThrowingWhenNullSource(KPSYS_API_KEY, new EToEConverter<>());
        bean.registerKeyReturningNullWhenNullSource(LICENCE_KEY, new EToEConverter<>());
        bean.registerKeyThrowingWhenNullSource(CENTRAL_INDEX_ENABLED, StringToBooleanConverter.notAllowingNullSource());
        bean.registerKeyReturningValueWhenNullSource(SERVICES, new StringToStringListConverter(), List::of);

        bean.registerKeyThrowingWhenNullSource(DEPARTMENT_CREATION_ACCEPTABLE_PARENT_DEPARTMENTS_SHOW_ALL_CHILDS, StringToBooleanConverter.notAllowingNullSource());
        bean.registerKeyReturningValueWhenNullSource(DEPARTMENT_CREATION_ACCEPTABLE_PARENT_DEPARTMENTS, new StringToIntegerListConverter(), List::of);
        bean.registerKeyThrowingWhenNullSource(DEPARTMENT_CREATION_CUSTOM_PARENT_DOMAIN_ENABLED, StringToBooleanConverter.notAllowingNullSource());
        bean.registerKeyReturningNullWhenNullSource(DEPARTMENT_CREATION_CUSTOM_PARENT_DOMAIN_VALUE, new EToEConverter<>());

        bean.registerKeyReturningNullWhenNullSource(LIBRARY_NAME, new EToEConverter<>());
        bean.registerKeyReturningNullWhenNullSource(LIBRARY_STREET, new EToEConverter<>());
        bean.registerKeyReturningNullWhenNullSource(LIBRARY_POSTAL_CODE, new EToEConverter<>());
        bean.registerKeyReturningNullWhenNullSource(LIBRARY_CITY, new EToEConverter<>());
        bean.registerKeyReturningNullWhenNullSource(LIBRARY_EMAIL, new EToEConverter<>());
        bean.registerKeyReturningNullWhenNullSource(LIBRARY_PHONE_NUMBER, new EToEConverter<>());
        bean.registerKeyReturningNullWhenNullSource(LIBRARY_CIN, new EToEConverter<>());
        bean.registerKeyReturningNullWhenNullSource(LIBRARY_VATIN, new EToEConverter<>());
        bean.registerKeyReturningValueWhenNullSource(LIBRARY_OPENING_MONDAY, StringToAnyListConverter.create(new StringToTimeRangeConverter(), StringToStringListConverter.DELIMITER_COMMA), List::of);
        bean.registerKeyReturningValueWhenNullSource(LIBRARY_OPENING_TUESDAY, StringToAnyListConverter.create(new StringToTimeRangeConverter(), StringToStringListConverter.DELIMITER_COMMA), List::of);
        bean.registerKeyReturningValueWhenNullSource(LIBRARY_OPENING_WEDNESDAY, StringToAnyListConverter.create(new StringToTimeRangeConverter(), StringToStringListConverter.DELIMITER_COMMA), List::of);
        bean.registerKeyReturningValueWhenNullSource(LIBRARY_OPENING_THURSDAY, StringToAnyListConverter.create(new StringToTimeRangeConverter(), StringToStringListConverter.DELIMITER_COMMA), List::of);
        bean.registerKeyReturningValueWhenNullSource(LIBRARY_OPENING_FRIDAY, StringToAnyListConverter.create(new StringToTimeRangeConverter(), StringToStringListConverter.DELIMITER_COMMA), List::of);
        bean.registerKeyReturningValueWhenNullSource(LIBRARY_OPENING_SATURDAY, StringToAnyListConverter.create(new StringToTimeRangeConverter(), StringToStringListConverter.DELIMITER_COMMA), List::of);
        bean.registerKeyReturningValueWhenNullSource(LIBRARY_OPENING_SUNDAY, StringToAnyListConverter.create(new StringToTimeRangeConverter(), StringToStringListConverter.DELIMITER_COMMA), List::of);

        //CORE
        bean.registerKeyThrowingWhenNullSource(DEFAULT_LOCALE, new StringToLocaleConverter()); // moved to CoreSettingKeys

        //FILE STREAMER
        bean.registerKeyThrowingWhenNullSource(FileSettingKeys.BLOB_DIR_ENABLED, StringToBooleanConverter.notAllowingNullSource());
        bean.registerKeyThrowingWhenNullSource(FileSettingKeys.BLOB_DIR, new EToEConverter<>());
        bean.registerKeyThrowingWhenNullSource(FileSettingKeys.PORTARO_BLOB_DIR_FILES_DIRECT_LOAD, StringToBooleanConverter.notAllowingNullSource());

        //EMAIL
        bean.registerKeyThrowingWhenNullSource(SMTP_USE_SENDER_AND_FROM, StringToBooleanConverter.notAllowingNullSource());
        bean.registerKeyReturningNullWhenNullSource(SMTP_FROM_LIBRARY_MAILS_REPLY_TO_ADDRESS, new EToEConverter<>());
        bean.registerKeyThrowingWhenNullSource(SMTP_TRANSPORT_STRATEGY, new ConversionTableConverter<>(SmtpTransportStrategy.CONVERSION_TABLE));
        bean.registerKeyThrowingWhenNullSource(SMTP_AUTHENTICATION_ENABLED, StringToBooleanConverter.notAllowingNullSource());
        bean.registerKeyReturningNullWhenNullSource(SMTP_SENDER_ADDRESS, new EToEConverter<>());
        bean.registerKeyReturningNullWhenNullSource(SMTP_SENDER_NAME, new EToEConverter<>());
        bean.registerKeyReturningNullWhenNullSource(SMTP_SERVER_ADDRESS, new EToEConverter<>());
        bean.registerKeyReturningValueWhenNullSource(SMTP_SERVER_PORT, new StringToIntegerConverter(), () -> 25); //defaultni port 25
        bean.registerKeyReturningNullWhenNullSource(SMTP_SERVER_USERNAME, new EToEConverter<>());
        bean.registerKeyReturningNullWhenNullSource(SMTP_SERVER_PASSWORD, new EToEConverter<>());

        //SMS
        bean.registerKeyThrowingWhenNullSource(SMS_ENABLED, StringToBooleanConverter.notAllowingNullSource());

        //USER & AUTH
        bean.registerKeyReturningValueWhenNullSource(USER_INTERNAL_LOGIN_CREDENTIALS_PROPERTIES, new ChainingConverter<>(new StringToStringListConverter(DEFAULT_LIST_DELIMITERS).ignoring("-"), new ListToModifiedListConverter<>(InternalLoginCredentialsProperties::parse)), List::of);
        bean.registerKeyThrowingWhenNullSource(CasAuthSettingKeys.CAS_ENABLED, StringToBooleanConverter.notAllowingNullSource());
        bean.registerKeyReturningNullWhenNullSource(CasAuthSettingKeys.CAS_SYSTEM_URL, new EToEConverter<>());
        bean.registerKeyReturningNullWhenNullSource(CasAuthSettingKeys.CAS_LOGIN_URL, new EToEConverter<>());
        bean.registerKeyThrowingWhenNullSource(CasAuthSettingKeys.CAS_TICKET_VALIDATOR_PROTOCOL, new ConversionTableConverter<>(CasTicketValidatorProtocol.CONVERSION_TABLE));
        bean.registerKeyThrowingWhenNullSource(CasAuthSettingKeys.CAS_USER_IDENTIFIER, new EToEConverter<>());
        bean.registerKeyThrowingWhenNullSource(CasAuthSettingKeys.CAS_USER_ASSERTION_ATTRIBUTE, new EToEConverter<>());
        bean.registerKeyThrowingWhenNullSource(EzakAuthSettingKeys.EZAK_ENABLED, StringToBooleanConverter.notAllowingNullSource());
        bean.registerKeyReturningNullWhenNullSource(EzakAuthSettingKeys.EZAK_SYSTEM_URL, new EToEConverter<>());
        bean.registerKeyThrowingWhenNullSource(LdapAuthSettingKeys.LDAP_ENABLED, StringToBooleanConverter.notAllowingNullSource());
        bean.registerKeyReturningNullWhenNullSource(LdapAuthSettingKeys.LDAP_URL, new EToEConverter<>());
        bean.registerKeyReturningNullWhenNullSource(LdapAuthSettingKeys.LDAP_USER_SEARCH_FILTER_TEMPLATE, new EToEConverter<>());
        bean.registerKeyReturningNullWhenNullSource(LdapAuthSettingKeys.LDAP_BASE_DNS, new StringToStringListConverter(";"));
        bean.registerKeyThrowingWhenNullSource(LdapAuthSettingKeys.LDAP_CERTIFICATE_VERIFICATION_ENABLED, StringToBooleanConverter.notAllowingNullSource());
        bean.registerKeyReturningNullWhenNullSource(LdapAuthSettingKeys.LDAP_CONTEXT_CONNECT_USER_DN, new EToEConverter<>());
        bean.registerKeyReturningNullWhenNullSource(LdapAuthSettingKeys.LDAP_CONTEXT_CONNECT_USER_PASSWORD, new EToEConverter<>());
        bean.registerKeyThrowingWhenNullSource(MojeIDAuthorizationCodeAuthSettingKeys.MOJEID_ENABLED, StringToBooleanConverter.notAllowingNullSource());
        bean.registerKeyThrowingWhenNullSource(MojeIDAuthorizationCodeAuthSettingKeys.MOJEID_SANDBOX_ENABLED, StringToBooleanConverter.notAllowingNullSource());
        bean.registerKeyReturningNullWhenNullSource(MojeIDAuthorizationCodeAuthSettingKeys.MOJEID_CLIENT_ID, new EToEConverter<>());
        bean.registerKeyReturningNullWhenNullSource(MojeIDAuthorizationCodeAuthSettingKeys.MOJEID_CLIENT_SECRET, new EToEConverter<>());
        bean.registerKeyReturningValueWhenNullSource(MojeIDAuthorizationCodeAuthSettingKeys.MOJEID_CLAIMS, new StringToStringListConverter(), List::of);
        bean.registerKeyReturningNullWhenNullSource(MojeIDAuthorizationCodeAuthSettingKeys.MOJEID_DEPARTMENT_FOR_REDIRECT_URL, new StringToIntegerConverter());
        bean.registerKeyReturningValueWhenNullSource(MojeIDAuthorizationCodeAuthSettingKeys.MOJEID_REQUIRED_PROPS, new StringToStringListConverter(), List::of);
        bean.registerKeyReturningValueWhenNullSource(MojeIDAuthorizationCodeAuthSettingKeys.MOJEID_TRUSTED_PROPS, new StringToStringListConverter(), List::of);
        bean.registerKeyThrowingWhenNullSource(MultifactorAuthSettingKeys.SECOND_FACTOR_ENABLED, StringToBooleanConverter.notAllowingNullSource());
        bean.registerKeyThrowingWhenNullSource(SideChannelAuthSettingKeys.EMAIL_AUTH_ENABLED, StringToBooleanConverter.notAllowingNullSource());
        bean.registerKeyThrowingWhenNullSource(SideChannelAuthSettingKeys.EMAIL_AUTH_TESTING_MODE, StringToBooleanConverter.notAllowingNullSource());
        bean.registerKeyThrowingWhenNullSource(OAuth2AuthorizationCodeAuthSettingKeys.GOOGLE_ENABLED, StringToBooleanConverter.notAllowingNullSource());
        bean.registerKeyReturningNullWhenNullSource(OAuth2AuthorizationCodeAuthSettingKeys.GOOGLE_CLIENT_ID, new EToEConverter<>());
        bean.registerKeyReturningNullWhenNullSource(OAuth2AuthorizationCodeAuthSettingKeys.GOOGLE_CLIENT_SECRET, new EToEConverter<>());
        bean.registerKeyReturningNullWhenNullSource(OAuth2AuthorizationCodeAuthSettingKeys.GOOGLE_DEPARTMENT_FOR_REDIRECT_URL, new StringToIntegerConverter());
        bean.registerKeyThrowingWhenNullSource(OAuth2AuthorizationCodeAuthSettingKeys.FACEBOOK_ENABLED, StringToBooleanConverter.notAllowingNullSource());
        bean.registerKeyReturningNullWhenNullSource(OAuth2AuthorizationCodeAuthSettingKeys.FACEBOOK_CLIENT_ID, new EToEConverter<>());
        bean.registerKeyReturningNullWhenNullSource(OAuth2AuthorizationCodeAuthSettingKeys.FACEBOOK_CLIENT_SECRET, new EToEConverter<>());
        bean.registerKeyReturningNullWhenNullSource(OAuth2AuthorizationCodeAuthSettingKeys.FACEBOOK_DEPARTMENT_FOR_REDIRECT_URL, new StringToIntegerConverter());
        bean.registerKeyThrowingWhenNullSource(BankIDAuthorizationCodeAuthSettingKeys.BANKID_ENABLED, StringToBooleanConverter.notAllowingNullSource());
        bean.registerKeyThrowingWhenNullSource(BankIDAuthorizationCodeAuthSettingKeys.BANKID_SANDBOX_ENABLED, StringToBooleanConverter.notAllowingNullSource());
        bean.registerKeyReturningNullWhenNullSource(BankIDAuthorizationCodeAuthSettingKeys.BANKID_OIDC_ISSUER_LOCATION,  new EToEConverter<>());
        bean.registerKeyReturningNullWhenNullSource(BankIDAuthorizationCodeAuthSettingKeys.BANKID_CLIENT_ID, new EToEConverter<>());
        bean.registerKeyReturningNullWhenNullSource(BankIDAuthorizationCodeAuthSettingKeys.BANKID_CLIENT_SECRET, new EToEConverter<>());
        bean.registerKeyReturningValueWhenNullSource(BankIDAuthorizationCodeAuthSettingKeys.BANKID_REQUIRED_PROPS, new StringToStringListConverter(), List::of);
        bean.registerKeyReturningValueWhenNullSource(BankIDAuthorizationCodeAuthSettingKeys.BANKID_SCOPES, new StringToStringListConverter(), List::of);
        bean.registerKeyReturningNullWhenNullSource(BankIDAuthorizationCodeAuthSettingKeys.BANKID_REGISTRATION_ACCEPTABLE_AGE, new StringToIntegerConverter());
        bean.registerKeyReturningNullWhenNullSource(BankIDAuthorizationCodeAuthSettingKeys.BANKID_DEPARTMENT_FOR_REDIRECT_URL, new StringToIntegerConverter());
        bean.registerKeyReturningValueWhenNullSource(PairingSettingKeys.READER_CATEGORIES_NOT_ELIGIBLE_FOR_EMAIL_AUTO_PAIRING, new StringToStringListConverter(), List::of);
        bean.registerKeyReturningNullWhenNullSource(ReaderAccountSettingKeys.READER_ACCOUNT_RETIREE_AGE_LIMIT, new StringToIntegerConverter());
        bean.registerKeyReturningNullWhenNullSource(ReaderAccountSettingKeys.READER_ACCOUNT_RETIREE_READER_CATEGORY_ID, new EToEConverter<>());
        bean.registerKeyReturningNullWhenNullSource(ReaderAccountSettingKeys.READER_ACCOUNT_CHILD_AGE_LIMIT, new StringToIntegerConverter());
        bean.registerKeyReturningNullWhenNullSource(ReaderAccountSettingKeys.READER_ACCOUNT_STUDENT_NOT_VERIFIED_AGE_LIMIT, new StringToIntegerConverter());
        bean.registerKeyReturningNullWhenNullSource(ReaderAccountSettingKeys.READER_ACCOUNT_STUDENT_AGE_LIMIT, new StringToIntegerConverter());
        bean.registerKeyReturningNullWhenNullSource(ReaderAccountSettingKeys.READER_ACCOUNT_CHILD_READER_CATEGORY_ID, new EToEConverter<>());
        bean.registerKeyReturningNullWhenNullSource(ReaderAccountSettingKeys.READER_ACCOUNT_STUDENT_READER_CATEGORY_ID, new EToEConverter<>());
        bean.registerKeyReturningNullWhenNullSource(ReaderAccountSettingKeys.READER_ACCOUNT_TEACHER_READER_CATEGORY_ID, new EToEConverter<>());
        bean.registerKeyReturningNullWhenNullSource(ReaderAccountSettingKeys.READER_ACCOUNT_BLIND_READER_CATEGORY_ID, new EToEConverter<>());
        bean.registerKeyReturningNullWhenNullSource(ReaderAccountSettingKeys.READER_ACCOUNT_ZTP_READER_CATEGORY_ID, new EToEConverter<>());
        bean.registerKeyReturningNullWhenNullSource(ReaderAccountSettingKeys.READER_ACCOUNT_CHILD_ZTP_READER_CATEGORY_ID, new EToEConverter<>());
        bean.registerKeyReturningValueWhenNullSource(ReaderAccountSettingKeys.READER_ACCOUNT_UNCHANGEABLE_READER_CATEGORIES, new StringToStringListConverter(), List::of);
        bean.registerKeyThrowingWhenNullSource(ReaderAccountSettingKeys.FORCE_PAYMENT_BEFORE_EXTENDING_REGISTRATION_PERIOD, StringToBooleanConverter.notAllowingNullSource());
        bean.registerKeyThrowingWhenNullSource(ReaderAccountSettingKeys.ENABLE_AUTOMATIC_READER_CATEGORY_RESOLVER, StringToBooleanConverter.notAllowingNullSource());
        bean.registerKeyThrowingWhenNullSource(ReaderAccountSettingKeys.INTERNAL_SOURCE_IS_TRUSTED, StringToBooleanConverter.notAllowingNullSource());
        bean.registerKeyThrowingWhenNullSource(ReaderAccountSettingKeys.ADD_NOTE_FOR_EXTERNAL_REGISTRATION, StringToBooleanConverter.notAllowingNullSource());
        bean.registerKeyReturningNullWhenNullSource(ReaderAccountSettingKeys.PROCEDURE_NAME_FOR_CUSTOM_FULL_REGISTRATION_BARCODE, new EToEConverter<>());
        bean.registerKeyThrowingWhenNullSource(Saml2IdentityProviderSettingKeys.SAML2_IDENTITY_PROVIDER_ENABLED, StringToBooleanConverter.notAllowingNullSource());
        bean.registerKeyReturningNullWhenNullSource(Saml2IdentityProviderSettingKeys.SAML2_IDENTITY_PROVIDER_ENTITY_ID, new EToEConverter<>());
        bean.registerKeyThrowingWhenNullSource(Saml2IdentityProviderSettingKeys.SAML2_IDENTITY_PROVIDER_ATTRIBUTES_CONFIRMATION_ENABLED, StringToBooleanConverter.notAllowingNullSource());
        bean.registerKeyThrowingWhenNullSource(Saml2IdentityProviderSettingKeys.SAML2_IDENTITY_PROVIDER_AUTH_REQUEST_SIGNATURE_REQUIRED, StringToBooleanConverter.notAllowingNullSource());
        bean.registerKeyReturningNullWhenNullSource(Saml2IdentityProviderSettingKeys.SAML2_IDENTITY_PROVIDER_CONTACT_PERSON_ID, new StringToIntegerConverter());
        bean.registerKeyReturningNullWhenNullSource(Saml2IdentityProviderSettingKeys.SAML2_IDENTITY_PROVIDER_SCOPE_DOMAIN, new EToEConverter<>());
        bean.registerKeyThrowingWhenNullSource(Saml2IdentityProviderSettingKeys.SAML2_IDENTITY_PROVIDER_USER_LIBRARY_ID_USER_IDENTIFIER, new EToEConverter<>());
        bean.registerKeyThrowingWhenNullSource(Saml2ServiceProviderSettingKeys.SAML2_SERVICE_PROVIDER_ENABLED, StringToBooleanConverter.notAllowingNullSource());
        bean.registerKeyReturningNullWhenNullSource(Saml2ServiceProviderSettingKeys.SAML2_SERVICE_PROVIDER_IDP_ENTITY_ID, new EToEConverter<>());
        bean.registerKeyReturningNullWhenNullSource(Saml2ServiceProviderSettingKeys.SAML2_SERVICE_PROVIDER_IDP_SSO_LOCATION, new EToEConverter<>());
        bean.registerKeyReturningNullWhenNullSource(Saml2ServiceProviderSettingKeys.SAML2_SERVICE_PROVIDER_IDP_CERTIFICATE, new EToEConverter<>());
        bean.registerKeyReturningNullWhenNullSource(Saml2ServiceProviderSettingKeys.SAML2_SERVICE_PROVIDER_USER_IDENTIFYING_ATTRIBUTE_NAME, new EToEConverter<>());
        bean.registerKeyThrowingWhenNullSource(USER_MAIL_ACTIVATION_ENABLED, StringToBooleanConverter.notAllowingNullSource());
        bean.registerKeyReturningValueWhenNullSource(USER_REQUIRED_FIELDS, new StringToStringListConverter(), List::of);
        bean.registerKeyReturningValueWhenNullSource(USER_REQUIRED_FIELDS_FOR_LIBRARIAN, new StringToStringListConverter(), List::of);
        bean.registerKeyReturningValueWhenNullSource(USER_SELF_CREATION_FIELDS, new StringToStringListConverter(), List::of);
        bean.registerKeyThrowingWhenNullSource(USER_SELF_CREATION_FIELDS_BY_CATEGORY, new JsonToObjectConverter<>(new TypeReference<>() {}));
        bean.registerKeyReturningValueWhenNullSource(USER_SELF_EDITATION_FIELDS, new StringToStringListConverter(), List::of);
        bean.registerKeyReturningValueWhenNullSource(READER_EDITABLE_FIELDS_BY_EDITOR, new StringToStringListConverter(), List::of);
        bean.registerKeyThrowingWhenNullSource(RegistrationSettingKeys.EXTERNAL_FULL_REGISTRATION_ENABLED, StringToBooleanConverter.notAllowingNullSource());
        bean.registerKeyReturningValueWhenNullSource(RegistrationSettingKeys.NOT_AUTO_EXTENDING_CATEGORIES_ON_REGISTRATION, new StringToStringListConverter(), List::of);
        bean.registerKeyThrowingWhenNullSource(UserSettingKeys.USER_WITH_NET_ID_IS_EXTERNAL, StringToBooleanConverter.notAllowingNullSource());
        bean.registerKeyThrowingWhenNullSource(UserSettingKeys.USER_DISCOUNT_REQUEST_ENABLED, StringToBooleanConverter.notAllowingNullSource());
        bean.registerKeyThrowingWhenNullSource(UserSettingKeys.NO_INTERNET_ACCESS_FOR_FULL_REGISTRATION_USER, StringToBooleanConverter.notAllowingNullSource());
        bean.registerKeyThrowingWhenNullSource(UserSettingKeys.DISABLE_AUTOMATIC_REGISTRATION_DEBT_GENERATION, StringToBooleanConverter.notAllowingNullSource());
        bean.registerKeyThrowingWhenNullSource(UserSettingKeys.USER_WITH_GUID_IS_EXTERNAL, StringToBooleanConverter.notAllowingNullSource());
        bean.registerKeyReturningNullWhenNullSource(UserSettingKeys.USER_NEW_DISCOUNT_REQUEST_NOTIFY_EMAIL, new EToEConverter<>());
        bean.registerKeyThrowingWhenNullSource(CredentialsRegistrationSettingKeys.USER_CREDENTIALS_REGISTRATION_ENABLED, StringToBooleanConverter.notAllowingNullSource());
        bean.registerKeyReturningValueWhenNullSource(CredentialsRegistrationSettingKeys.USER_CREDENTIALS_REGISTRATION_IDENTIFIER_PROPS, new StringToStringListConverter(), List::of);
        bean.registerKeyThrowingWhenNullSource(UserSettingKeys.USER_PASSWORD_REGEX, new EToEConverter<>());
        bean.registerKeyThrowingWhenNullSource(UserSettingKeys.USER_BARCODE_CORE_MAX_LENGTH, new StringToIntegerConverter());
        bean.registerKeyReturningNullWhenNullSource(UserSettingKeys.USER_DEFAULT_DEPARTMENT, new StringToIntegerConverter());
        bean.registerKeyThrowingWhenNullSource(MONEY_ENABLED, StringToBooleanConverter.notAllowingNullSource());
        bean.registerKeyThrowingWhenNullSource(FULL_REGISTRATION_ENABLED, StringToBooleanConverter.notAllowingNullSource());
        bean.registerKeyThrowingWhenNullSource(FORGOTTEN_CREDENTIALS_ENABLED, StringToBooleanConverter.notAllowingNullSource());
        bean.registerKeyReturningNullWhenNullSource(DEFAULT_BY_READER_FULL_REGISTRATION_READER_CATEGORY, new EToEConverter<>());
        bean.registerKeyReturningNullWhenNullSource(DEFAULT_EDITOR_ACCOUNT_GROUP, new StringToIntegerConverter());
        bean.registerKeyThrowingWhenNullSource(SHOW_USER_REGISTRATION_AGREEMENT_PRINT_BUTTON, StringToBooleanConverter.notAllowingNullSource());
        bean.registerKeyThrowingWhenNullSource(SHOW_USER_RECEIPT_OF_PAYMENT_PRINT_BUTTON, StringToBooleanConverter.notAllowingNullSource());
        bean.registerKeyThrowingWhenNullSource(SHOW_USER_LOAN_CONFIRMATION_PRINT_BUTTON, StringToBooleanConverter.notAllowingNullSource());
        bean.registerKeyThrowingWhenNullSource(SHOW_USER_PREFERENCES_BUTTON, StringToBooleanConverter.notAllowingNullSource());
        bean.registerKeyThrowingWhenNullSource(READER_SELF_REGISTRATION_EXTEND_FOR_ALL_USERS, StringToBooleanConverter.notAllowingNullSource());
        bean.registerKeyThrowingWhenNullSource(AUTOGENERATE_USER_CARD_NUMBER, StringToBooleanConverter.notAllowingNullSource());
        bean.registerKeyThrowingWhenNullSource(AUTOGENERATE_USER_BARCODE, StringToBooleanConverter.notAllowingNullSource());
        bean.registerKeyReturningValueWhenNullSource(UserSettingKeys.USER_BARCODE_PREFIX, new EToEConverter<>(), () -> "");
        bean.registerKeyReturningValueWhenNullSource(READER_SELF_REGISTRATION_EXTEND_READER_CATEGORIES, new StringToStringListConverter(), List::of);
        bean.registerKeyThrowingWhenNullSource(SHOW_TERMS_AND_CONDITIONS_IF_EXTENDING_REGISTRATION, StringToBooleanConverter.notAllowingNullSource());
        bean.registerKeyThrowingWhenNullSource(SHOW_USER_STATEMENT_IF_EXTENDING_REGISTRATION, StringToBooleanConverter.notAllowingNullSource());
        bean.registerKeyThrowingWhenNullSource(REGISTRATION_PERIOD_EXTENSION_THRESHOLD_DAYS, new StringToIntegerConverter());
        bean.registerKeyThrowingWhenNullSource(UserSettingKeys.EMPLOYEE_READER_CATEGORIES, new StringToStringListConverter());
        bean.registerKeyThrowingWhenNullSource(UserSettingKeys.USER_BARCODE_TYPES, StringToAnyListConverter.create(new ConversionTableConverter<>(BarCodeType.CONVERSION_TABLE)));

        //RECORD
        bean.registerKeyThrowingWhenNullSource(RecordSettingKeys.PORTARO_DETAIL_LOAD_ENABLED, StringToBooleanConverter.notAllowingNullSource());
        bean.registerKeyThrowingWhenNullSource(RecordSettingKeys.PORTARO_DETAIL_RECORD_FIELD_LOAD_ENABLED, StringToBooleanConverter.notAllowingNullSource());
        bean.registerKeyThrowingWhenNullSource(RecordSettingKeys.CYRILLIC_ENABLED, StringToBooleanConverter.notAllowingNullSource());
        bean.registerKeyReturningValueWhenNullSource(RecordSettingKeys.FORBIDDEN_RECORDS, StringToUuidConverter.DELIMITED_LIST_CONVERTER, List::of);
        bean.registerKeyReturningValueWhenNullSource(RecordSettingKeys.FORBIDDEN_RECORD_STATUSES, new StringToIntegerListConverter(), List::of);
        bean.registerKeyReturningValueWhenNullSource(RecordSettingKeys.AUTHORITY_FONDS, new StringToIntegerListConverter(), List::of);
        bean.registerKeyReturningValueWhenNullSource(RecordSettingKeys.DOCUMENT_FONDS, new StringToIntegerListConverter(), List::of);
        bean.registerKeyReturningValueWhenNullSource(BUILDINGS_DEFAULT, new StringToIntegerListConverter(), List::of);
        bean.registerKeyThrowingWhenNullSource(RECORD_MARC_TAB_ENABLED, StringToBooleanConverter.notAllowingNullSource());
        bean.registerKeyThrowingWhenNullSource(AUTHORITY_DETAIL_TAB_ENABLED, StringToBooleanConverter.notAllowingNullSource());
        bean.registerKeyThrowingWhenNullSource(DOCUMENT_DETAIL_TAB_ENABLED, StringToBooleanConverter.notAllowingNullSource());
        bean.registerKeyThrowingWhenNullSource(DOCUMENT_EXEMPLARS_TAB_ENABLED, StringToBooleanConverter.notAllowingNullSource());
        bean.registerKeyThrowingWhenNullSource(SHOW_ALL_RECORD_FIELDS_ENABLED, StringToBooleanConverter.notAllowingNullSource());
        bean.registerKeyThrowingWhenNullSource(ONLY_USED_AUTHORITIES_SHOW_ENABLED, StringToBooleanConverter.notAllowingNullSource());
        bean.registerKeyReturningNullWhenNullSource(AUTHORITY_TEMPLATE, new EToEConverter<>());
        bean.registerKeyReturningNullWhenNullSource(DOCUMENT_UNDER_COVER_TEMPLATE, new EToEConverter<>());
        bean.registerKeyReturningNullWhenNullSource(DOCUMENT_NEXT_TO_HEADER_TEMPLATE, new EToEConverter<>());
        bean.registerKeyReturningNullWhenNullSource(DOCUMENT_NEXT_TO_COVER_TEMPLATE, new EToEConverter<>());
        bean.registerKeyThrowingWhenNullSource(SIMILAR_DOCUMENTS_LIMIT, new StringToIntegerConverter());
        bean.registerKeyReturningNullWhenNullSource(AUTHORITY_HIERARCHY_ROOT, StringToUuidConverter.INSTANCE);
        bean.registerKeyThrowingWhenNullSource(CITATION_SERVICE, new EToEConverter<>());
        bean.registerKeyReturningValueWhenNullSource(CITATION_DOCTYPES_BY_FONDS, new StringToIntegerAnyMapConverter<>(new EToEConverter<>()), Map::of);
        bean.registerKeyThrowingWhenNullSource(COMMENT_STYLE, new ConversionTableConverter<>(CommentStyle.CONVERSION_TABLE));
        bean.registerKeyReturningNullWhenNullSource(SEARCH_ELSEWHERE_ITEMS, SearchElsewhereItem.createConverter());
        bean.registerKeyThrowingWhenNullSource(DOCUMENT_DETAIL_FIELDS_FILTER, new StringToFieldTypeFilterConverter(false));
        bean.registerKeyReturningValueWhenNullSource(RECORD_DETAIL_FIELDS_FILTERS_BY_FONDS, new StringToIntegerFieldTypeFilterMapConverter<>(), Map::of);
        bean.registerKeyThrowingWhenNullSource(SHOW_EDITED_INDICATORS_ENABLED, StringToBooleanConverter.notAllowingNullSource());
        bean.registerKeyThrowingWhenNullSource(SHOW_EDITED_FIELD_IDENTIFIERS_ENABLED, StringToBooleanConverter.notAllowingNullSource());
        bean.registerKeyThrowingWhenNullSource(PUBLISHING_DOCUMENT_FINISHED_CATALOGIZATION, StringToBooleanConverter.notAllowingNullSource());
        bean.registerKeyThrowingWhenNullSource(DOCUMENT_ARTICLES_TAB_ENABLED, StringToBooleanConverter.notAllowingNullSource());
        bean.registerKeyThrowingWhenNullSource(DOCUMENT_PARTS_CONTAINING_FIELD_NUMBERS, new StringToIntegerListConverter());
        bean.registerKeyThrowingWhenNullSource(RATING_ENABLED, StringToBooleanConverter.notAllowingNullSource());
        bean.registerKeyThrowingWhenNullSource(HOLDING_ASSIGNABLE_FROM_CONTEXTS, new StringToIntegerListConverter());
        bean.registerKeyThrowingWhenNullSource(NEWEST_SEARCH_VIEW_NAME, new EToEConverter<>());

        //SEARCH
        bean.registerKeyReturningValueWhenNullSource(RecordSettingKeys.NEWS_SLIDER_BUILDINGS, new StringToIntegerListConverter(), List::of);
        bean.registerKeyReturningValueWhenNullSource(RecordSettingKeys.NEWS_EXEMPLAR_STATUSES, new StringToIntegerListConverter(), List::of);
        bean.registerKeyThrowingWhenNullSource(RecordSettingKeys.NEWS_WITH_COVER_PREFERING_ENABLED, StringToBooleanConverter.notAllowingNullSource());
        bean.registerKeyThrowingWhenNullSource(RecordSettingKeys.NEWS_INCLUDE_PERIODICAL_FONDS_ENABLED, StringToBooleanConverter.notAllowingNullSource());
        bean.registerKeyThrowingWhenNullSource(MOST_LENT_DOCUMENTS_WITH_PERIODICALS_ENABLED, StringToBooleanConverter.notAllowingNullSource());
        bean.registerKeyThrowingWhenNullSource(GLOBAL_SEARCH_TEMPLATE_1, new EToEConverter<>());
        bean.registerKeyReturningNullWhenNullSource(SEARCH_DOCUMENT_TEMPLATE, new EToEConverter<>());
        bean.registerKeyReturningNullWhenNullSource(SEARCH_DOCUMENT_MORE_TEMPLATE, new EToEConverter<>());
        bean.registerKeyReturningNullWhenNullSource(RecordSettingKeys.NEWS_TEMPLATE, new EToEConverter<>());
        bean.registerKeyReturningNullWhenNullSource(SEARCH_AUTHORITY_TEMPLATE, new EToEConverter<>());
        bean.registerKeyThrowingWhenNullSource(AUTHORITY_GLOBAL_SEARCH_QUERY_TEMPLATE, new EToEConverter<>());
        bean.registerKeyReturningValueWhenNullSource(AUTHORITY_INDEX_PREFIXES_BY_FONDS, new StringToIntegerAnyMapConverter<>(new EToEConverter<>()), Map::of);
        bean.registerKeyThrowingWhenNullSource(BASIC_MODE_SEARCH_FIELDS_COUNT, new StringToIntegerConverter());
        bean.registerKeyThrowingWhenNullSource(FORM_SEARCH_FIELDS, new StringToStringListConverter().throwOnBlankItems());
        bean.registerKeyThrowingWhenNullSource(DOCUMENT_SEARCH_PAGE_SIZE, new StringToIntegerConverter());
        bean.registerKeyThrowingWhenNullSource(AUTHORITY_SEARCH_PAGE_SIZE, new StringToIntegerConverter());
        bean.registerKeyReturningValueWhenNullSource(FACETS, new StringToStringListConverter(), List::of);
        bean.registerKeyReturningValueWhenNullSource(DEFAULT_EXPANDED_FACETS, new StringToStringListConverter(), List::of);
        bean.registerKeyThrowingWhenNullSource(FACET_KEYS_MAX_COUNT, new StringToIntegerConverter());
        bean.registerKeyReturningNullWhenNullSource(DOCUMENT_MIN_YEAR, new StringToIntegerConverter());
        bean.registerKeyThrowingWhenNullSource(RecordSettingKeys.NEWS_SLIDER_PRESEARCHED_COUNT, new StringToIntegerConverter());
        bean.registerKeyThrowingWhenNullSource(RecordSettingKeys.NEWS_SLIDER_SHOWED_COUNT, new StringToIntegerConverter());
        bean.registerKeyThrowingWhenNullSource(RecordSettingKeys.NEWS_SEARCH_MAX_AGE, new StringToIntegerConverter());
        bean.registerKeyReturningValueWhenNullSource(SEARCH_SORTINGS, StringToAnyListConverter.create(SortingItem::ofDefaultMessage), List::of);
        bean.registerKeyReturningValueWhenNullSource(AUTHORITY_SEARCH_SORTINGS, StringToAnyListConverter.create(SortingItem::ofDefaultMessage), List::of);
        bean.registerKeyThrowingWhenNullSource(ENABLED_LOCAL_DATASETS, new StringToStringListConverter().throwOnBlankItems());
        bean.registerKeyThrowingWhenNullSource(SHOW_ALL_ZSERVERS, StringToBooleanConverter.notAllowingNullSource());
        bean.registerKeyThrowingWhenNullSource(ENABLED_ZSERVERS, new StringToIntegerListConverter());
        bean.registerKeyThrowingWhenNullSource(GLOBAL_SEARCH_INPUT_ENABLED, StringToBooleanConverter.notAllowingNullSource());
        bean.registerKeyThrowingWhenNullSource(FORCE_DISABLE_SEARCH_IN_ROOT_DATASET, StringToBooleanConverter.notAllowingNullSource());

        //EXEMPLAR
        bean.registerKeyReturningValueWhenNullSource(FORBIDDEN_EXEMPLARS, new StringToIntegerListConverter(), List::of);
        bean.registerKeyReturningValueWhenNullSource(EXEMPLAR_STATUSES, new StringToIntegerListConverter(), List::of);
        bean.registerKeyReturningValueWhenNullSource(ExemplarSettingKeys.EXEMPLAR_STATUSES_EXPORT, new StringToIntegerListConverter(), List::of);
        bean.registerKeyThrowingWhenNullSource(ExemplarSettingKeys.EXPORT_EXEMPLAR_FIELD_NUMBER, new EToEConverter<>());
        bean.registerKeyThrowingWhenNullSource(ExemplarSettingKeys.INVENTORY_DISCARD_ONLY_TWICE_NOT_FOUND, StringToBooleanConverter.notAllowingNullSource());
        bean.registerKeyReturningNullWhenNullSource(BUILDING_SCOPE_ACCESS_NUMBERS_UNIQUENESS_ENABLED, StringToBooleanConverter.notAllowingNullSource());
        bean.registerKeyThrowingWhenNullSource(TITLE_SIGNATURES_ENABLED, StringToBooleanConverter.notAllowingNullSource());
        bean.registerKeyThrowingWhenNullSource(EXEMPLAR_TABS_BY_BUILDINGS_ENABLED, StringToBooleanConverter.notAllowingNullSource());
        bean.registerKeyThrowingWhenNullSource(DISCARD_NUMBERS_WITHIN_BUILDING_ENABLED, StringToBooleanConverter.notAllowingNullSource());
        bean.registerKeyThrowingWhenNullSource(REGAL_MAP_PROPERTY, new ConversionTableConverter<>(ExemplarConstants.COLUMNS_AND_PROPS_MAP));
        bean.registerKeyThrowingWhenNullSource(EXEMPLAR_BAR_CODE_MAX_LENGTH, new StringToIntegerConverter());
        bean.registerKeyReturningValueWhenNullSource(ISSUE_COLUMNS, StringToAnyListConverter.create(new ConversionTableConverter<>(ExemplarConstants.COLUMNS_AND_PROPS_MAP)), List::of);
        bean.registerKeyReturningValueWhenNullSource(EXEMPLAR_DEFAULT_COLUMNS, StringToAnyListConverter.create(new ConversionTableConverter<>(ExemplarConstants.COLUMNS_AND_PROPS_MAP)), List::of);
        bean.registerKeyReturningValueWhenNullSource(EXEMPLAR_COLUMNS_BY_FONDS, new StringToIntegerAnyMapConverter<>(StringToAnyListConverter.create(new ConversionTableConverter<>(ExemplarConstants.COLUMNS_AND_PROPS_MAP))), Map::of);
        bean.registerKeyReturningValueWhenNullSource(EDITABLE_ISSUE_PROPS, StringToAnyListConverter.create(new ConversionTableConverter<>(ExemplarConstants.COLUMNS_AND_PROPS_MAP)), List::of);
        bean.registerKeyReturningValueWhenNullSource(EDITABLE_EXEMPLAR_PROPS, StringToAnyListConverter.create(new ConversionTableConverter<>(ExemplarConstants.COLUMNS_AND_PROPS_MAP)), List::of);
        bean.registerKeyReturningValueWhenNullSource(EXEMPLAR_SORT_PROPS, StringToAnyListConverter.create(new StringToSortOrderConverter(new ConversionTableConverter<>(ExemplarConstants.COLUMNS_AND_PROPS_MAP))), List::of);
        bean.registerKeyReturningValueWhenNullSource(BINDING_SORT_PROPS, StringToAnyListConverter.create(new StringToSortOrderConverter(new ConversionTableConverter<>(ExemplarConstants.COLUMNS_AND_PROPS_MAP))), List::of);
        bean.registerKeyReturningValueWhenNullSource(ISSUE_SORT_PROPS, StringToAnyListConverter.create(new StringToSortOrderConverter(new ConversionTableConverter<>(ExemplarConstants.COLUMNS_AND_PROPS_MAP))), List::of);
        bean.registerKeyThrowingWhenNullSource(EXEMPLAR_BARCODE_TYPES,  StringToAnyListConverter.create(new ConversionTableConverter<>(BarCodeType.CONVERSION_TABLE)));

        //LOAN
        bean.registerKeyThrowingWhenNullSource(LoanSettingKeys.LOANS_ENABLED, StringToBooleanConverter.notAllowingNullSource());
        bean.registerKeyThrowingWhenNullSource(LoanSettingKeys.ORDERING_TYPE, new EToEConverter<>());
        bean.registerKeyThrowingWhenNullSource(LoanSettingKeys.RESERVING_TYPE, new EToEConverter<>());
        bean.registerKeyThrowingWhenNullSource(LoanSettingKeys.DELAYED_LOANS_RENEWAL_ENABLED, StringToBooleanConverter.notAllowingNullSource());
        bean.registerKeyThrowingWhenNullSource(LoanSettingKeys.DEBTOR_LOANS_RENEWAL_BY_READER_ENABLED, StringToBooleanConverter.notAllowingNullSource());
        bean.registerKeyThrowingWhenNullSource(LoanSettingKeys.EXPIRED_READER_LOANS_RENEWAL_BY_READER_ENABLED, StringToBooleanConverter.notAllowingNullSource());
        bean.registerKeyThrowingWhenNullSource(LoanSettingKeys.RENEWAL_ENABLED, StringToBooleanConverter.notAllowingNullSource());
        bean.registerKeyThrowingWhenNullSource(LoanSettingKeys.PROCESSED_ORDERS_CANCELLING_ENABLED, StringToBooleanConverter.notAllowingNullSource());
        bean.registerKeyThrowingWhenNullSource(LoanSettingKeys.SENT_RESERVATIONS_CANCELLING_ENABLED, StringToBooleanConverter.notAllowingNullSource());
        bean.registerKeyThrowingWhenNullSource(IllSettingKeys.ILL_PORTARO_PROCESS_ENABLED, StringToBooleanConverter.notAllowingNullSource());
        bean.registerKeyReturningNullWhenNullSource(IllSettingKeys.DEFAULT_SEEKING_PROCESS_DEPARTMENT, new StringToIntegerConverter());
        bean.registerKeyReturningNullWhenNullSource(IllSettingKeys.DEFAULT_PROVISION_PROCESS_DEPARTMENT, new StringToIntegerConverter());
        bean.registerKeyThrowingWhenNullSource(IllSettingKeys.LIBRARY_NAME_GENERATION_MODE, new ConversionTableConverter<>(LibraryNameGenerationMode.INI_CONVERSION_TABLE));
        bean.registerKeyThrowingWhenNullSource(IllSettingKeys.LIBRARY_NAME_SIGLA_INSERTION_MODE, new ConversionTableConverter<>(LibraryNameSiglaInsertionMode.INI_CONVERSION_TABLE));
        bean.registerKeyThrowingWhenNullSource(IllSettingKeys.DEFAULT_MVS_REQUEST_INTEREST_DAYS, new StringToIntegerConverter());
        bean.registerKeyThrowingWhenNullSource(IllSettingKeys.MVS_ACTIVE_ENABLED, StringToBooleanConverter.notAllowingNullSource());
        bean.registerKeyThrowingWhenNullSource(IllSettingKeys.MVS_PASSIVE_ENABLED, StringToBooleanConverter.notAllowingNullSource());
        bean.registerKeyThrowingWhenNullSource(IllSettingKeys.PROVIDERS_REGISTRATION_ENABLED, StringToBooleanConverter.notAllowingNullSource());
        bean.registerKeyThrowingWhenNullSource(LoanSettingKeys.LENDER_READER_VERIFYING_ENABLED, StringToBooleanConverter.notAllowingNullSource());
        bean.registerKeyThrowingWhenNullSource(LoanSettingKeys.EDITABLE_SUBDEPARTMENTS_ONLY_LOANS_SHOW_ENABLED, StringToBooleanConverter.notAllowingNullSource());
        bean.registerKeyThrowingWhenNullSource(LoanSettingKeys.LOANS_PROVIDED_TO_DEPARTMENTS, new StringToIntegerListConverter());
        bean.registerKeyThrowingWhenNullSource(LoanSettingKeys.DEFAULT_LOAN_REQUEST_INTEREST_DAYS, new StringToIntegerConverter());
        bean.registerKeyReturningNullWhenNullSource(LoanSettingKeys.ORDERS_RECIPIENT_EMAIL, new EToEConverter<>());
        bean.registerKeyReturningNullWhenNullSource(EXTERNAL_RESOURCES_FOND, new StringToIntegerConverter().nullIfEmpty());
        bean.registerKeyThrowingWhenNullSource(LoanSettingKeys.RESERVATION_DATE_TO_REQUIRED, StringToBooleanConverter.notAllowingNullSource());
        bean.registerKeyThrowingWhenNullSource(LoanSettingKeys.LENDING_CHECK_DIFFERENT_DEPARTMENT, StringToBooleanConverter.notAllowingNullSource());
        bean.registerKeyThrowingWhenNullSource(LoanSettingKeys.LENDING_CHECK_DIFFERENT_LOCATION, StringToBooleanConverter.notAllowingNullSource());
        bean.registerKeyThrowingWhenNullSource(LoanSettingKeys.LENDING_CHECK_USER_HAS_DEBT, StringToBooleanConverter.notAllowingNullSource());
        bean.registerKeyThrowingWhenNullSource(LoanSettingKeys.RETURNING_CHECK_DIFFERENT_DEPARTMENT, StringToBooleanConverter.notAllowingNullSource());
        bean.registerKeyThrowingWhenNullSource(LoanSettingKeys.RETURNING_CHECK_DIFFERENT_LOCATION, StringToBooleanConverter.notAllowingNullSource());
        bean.registerKeyThrowingWhenNullSource(LoanSettingKeys.SHOW_LIBRARIAN_MESSAGE, StringToBooleanConverter.notAllowingNullSource());
        bean.registerKeyThrowingWhenNullSource(LoanSettingKeys.LENDING_RELAXED_DEBT_LIMIT_VALUE, new StringToDoubleConverter());
        bean.registerKeyThrowingWhenNullSource(LoanSettingKeys.LENDING_RELAXED_DEBT_LIMIT_AMOUNT_TYPES, new StringToIntegerListConverter());
        bean.registerKeyThrowingWhenNullSource(LoanSettingKeys.LENDING_WARN_MAX_ONE_EXEMPLAR_OF_SAME_RECORD, StringToBooleanConverter.notAllowingNullSource());
        bean.registerKeyThrowingWhenNullSource(LoanSettingKeys.LENDING_MAX_ONE_PHYSICAL_EXEMPLAR_OF_ONE_RECORD_BY_NON_LIBRARIAN, StringToBooleanConverter.notAllowingNullSource());
        bean.registerKeyThrowingWhenNullSource(LoanSettingKeys.PALMKNIHY_ENABLED, StringToBooleanConverter.notAllowingNullSource());
        bean.registerKeyThrowingWhenNullSource(LoanSettingKeys.PALMKNIHY_PDF_ENABLED, StringToBooleanConverter.notAllowingNullSource());
        bean.registerKeyThrowingWhenNullSource(LoanSettingKeys.PALMKNIHY_AUDIO_ENABLED, StringToBooleanConverter.notAllowingNullSource());
        bean.registerKeyThrowingWhenNullSource(LoanSettingKeys.LEVNAKNIHOVNA_ENABLED, StringToBooleanConverter.notAllowingNullSource());
        bean.registerKeyThrowingWhenNullSource(LoanSettingKeys.FLEXIBOOKS_ENABLED, StringToBooleanConverter.notAllowingNullSource());
        bean.registerKeyThrowingWhenNullSource(LoanSettingKeys.LOAN_MIGRATION_ENABLED, StringToBooleanConverter.notAllowingNullSource());

        // EXTERNAL LOAN
        bean.registerKeyThrowingWhenNullSource(LoanSettingKeys.EXT_LOANS_CHECK_DEBTS, StringToBooleanConverter.notAllowingNullSource());

        //PAYMENT
        bean.registerKeyThrowingWhenNullSource(PaymentProviderSettingKeys.FORBIDDEN_GATEWAY_PAYMENT_TYPES, new StringToIntegerListConverter());
        bean.registerKeyThrowingWhenNullSource(PaymentProviderSettingKeys.GATEWAY_ALLOWED_USER_IDS, new StringToIntegerListConverter());
        bean.registerKeyReturningNullWhenNullSource(PaymentProviderSettingKeys.TERMS_AND_CONDITIONS_URL, new StringToNotBlankStringConverter());
        bean.registerKeyThrowingWhenNullSource(PaymentSettingKeys.DEPARTMENT_HIERARCHY_ENABLED, StringToBooleanConverter.notAllowingNullSource());

        //GOPAY
        bean.registerKeyThrowingWhenNullSource(PaymentProviderSettingKeys.GOPAY_ENABLED, StringToBooleanConverter.notAllowingNullSource());
        bean.registerKeyReturningNullWhenNullSource(PaymentProviderSettingKeys.GOPAY_API_URL, new StringToNotBlankStringConverter());
        bean.registerKeyReturningNullWhenNullSource(PaymentProviderSettingKeys.GOPAY_CLIENT_ID, new StringToNotBlankStringConverter());
        bean.registerKeyReturningNullWhenNullSource(PaymentProviderSettingKeys.GOPAY_CLIENT_SECRET, new StringToNotBlankStringConverter());
        bean.registerKeyReturningNullWhenNullSource(PaymentProviderSettingKeys.GOPAY_GO_ID, new StringToNotBlankStringConverter());

        //GPWEBPAY
        bean.registerKeyThrowingWhenNullSource(PaymentProviderSettingKeys.GPWEBPAY_ENABLED, StringToBooleanConverter.notAllowingNullSource());
        bean.registerKeyReturningNullWhenNullSource(PaymentProviderSettingKeys.GPWEBPAY_ORDER_URL, new StringToNotBlankStringConverter());
        bean.registerKeyReturningNullWhenNullSource(PaymentProviderSettingKeys.GPWEBPAY_MERCHANT_NUMBER, new StringToNotBlankStringConverter());

        //CSOBGW
        bean.registerKeyThrowingWhenNullSource(CsobGwSettingKeys.CSOBGW_ENABLED, StringToBooleanConverter.notAllowingNullSource());
        bean.registerKeyThrowingWhenNullSource(CsobGwSettingKeys.CSOBGW_PROD_ENVIROMENT, StringToBooleanConverter.notAllowingNullSource());
        bean.registerKeyReturningNullWhenNullSource(CsobGwSettingKeys.CSOBGW_MERCHANT_ID, new StringToNotBlankStringConverter());

        //VIRTUAL TERMINAL
        bean.registerKeyThrowingWhenNullSource(ManualPaymentProviderSettingKeys.TERMINAL_VIRTUAL_ENABLED, StringToBooleanConverter.notAllowingNullSource());

        // GDPR
        bean.registerKeyThrowingWhenNullSource(SHOW_ANONYMIZATION_QUESTION_DIALOG, StringToBooleanConverter.notAllowingNullSource());

        // POPS
        bean.registerKeyThrowingWhenNullSource(AGREEMENT_STYLE, new ConversionTableConverter<>(AgreementStyle.CONVERSION_TABLE));

        //API
        bean.registerKeyReturningNullWhenNullSource(OaiSettingKeys.DOCUMENT_ID_PATTERN, new EToEConverter<>());
        bean.registerKeyReturningNullWhenNullSource(OaiSettingKeys.AUTHORITY_ID_PATTERN, new EToEConverter<>());
        bean.registerKeyThrowingWhenNullSource(OaiSettingKeys.PAGE_SIZE, new StringToIntegerConverter());

        // OBALKYKNIH
        bean.registerKeyThrowingWhenNullSource(ObalkyknihSettingKeys.OBALKYKNIH_ENABLED, StringToBooleanConverter.notAllowingNullSource());
        bean.registerKeyThrowingWhenNullSource(ObalkyknihSettingKeys.OBALKYKNIH_AS_ENABLED, StringToBooleanConverter.notAllowingNullSource());
        bean.registerKeyThrowingWhenNullSource(ObalkyknihSettingKeys.OBALKYKNIH_DOWNLOAD_TOC_SCAN, StringToBooleanConverter.notAllowingNullSource());
        bean.registerKeyThrowingWhenNullSource(ObalkyknihSettingKeys.OBALKYKNIH_DOWNLOAD_TOC_SCAN_PREVIEW, StringToBooleanConverter.notAllowingNullSource());
        bean.registerKeyThrowingWhenNullSource(ObalkyknihSettingKeys.OBALKYKNIH_DOWNLOAD_REFERENCES_SCAN, StringToBooleanConverter.notAllowingNullSource());
        bean.registerKeyThrowingWhenNullSource(ObalkyknihSettingKeys.OBALKYKNIH_RESCAN_INTERVAL, new StringToDurationConverter(ChronoUnit.DAYS));

        // EXPORT
        bean.registerKeyThrowingWhenNullSource(RecordExportSettingKeys.FIELD_910_IN_MARC_EXPORT_EXISTING_REMOVING_ENABLED, StringToBooleanConverter.notAllowingNullSource());
        bean.registerKeyThrowingWhenNullSource(RecordExportSettingKeys.FIELD_910_IN_MARC_EXPORT_ADDING_ENABLED, StringToBooleanConverter.notAllowingNullSource());
        bean.registerKeyThrowingWhenNullSource(RecordExportSettingKeys.CPK_EXPORT_ENABLED, StringToBooleanConverter.notAllowingNullSource());
        bean.registerKeyThrowingWhenNullSource(RecordExportSettingKeys.CPK_EXPORT_EXEMPLARS_ENABLED, StringToBooleanConverter.notAllowingNullSource());
        bean.registerKeyThrowingWhenNullSource(RecordExportSettingKeys.ATTACHMENTS_IN_MARC_EXPORT_ENABLED, StringToBooleanConverter.notAllowingNullSource());
        bean.registerKeyThrowingWhenNullSource(RecordExportSettingKeys.MEDIA_VIEWER_LINK_IN_MARC_EXPORT_ENABLED, StringToBooleanConverter.notAllowingNullSource());

        bean.registerKeyReturningNullWhenNullSource(DEFAULT_LOCATION, new StringToIntegerConverter().nullIfEmpty());
        bean.registerKeyReturningNullWhenNullSource(DEFAULT_STATUS, new StringToIntegerConverter());
        bean.registerKeyReturningNullWhenNullSource(DEFAULT_THEMATIC_GROUP, new EToEConverter<>());
        bean.registerKeyReturningNullWhenNullSource(DEFAULT_ACQUISITION_WAY, new EToEConverter<>());
        bean.registerKeyReturningNullWhenNullSource(DEFAULT_LOAN_CATEGORY, new EToEConverter<>());
        bean.registerKeyReturningNullWhenNullSource(DEFAULT_READER_CATEGORY, new EToEConverter<>());
        bean.registerKeyReturningNullWhenNullSource(IllSettingKeys.DEFAULT_LIBRARY_READER_CATEGORY, new EToEConverter<>());
        bean.registerKeyReturningNullWhenNullSource(IllSettingKeys.DEFAULT_LOAN_CATEGORY, new EToEConverter<>());
        bean.registerKeyThrowingWhenNullSource(IllSettingKeys.COMMON_REFERENCE_NUMBER_TEMPLATE, new EToEConverter<>());
        bean.registerKeyReturningNullWhenNullSource(IllSettingKeys.SEEKED_SEEKING_SEEKER_REFERENCE_NUMBER_TEMPLATE, new EToEConverter<>());
        bean.registerKeyReturningNullWhenNullSource(IllSettingKeys.PROVIDED_SEEKING_PROVIDER_REFERENCE_NUMBER_TEMPLATE, new EToEConverter<>());
        bean.registerKeyThrowingWhenNullSource(IllSettingKeys.REFERENCE_NUMBERS_BY_YEAR, StringToBooleanConverter.notAllowingNullSource());
        bean.registerKeyThrowingWhenNullSource(IllSettingKeys.MVS_AMOUNT_TYPE_ID, new StringToIntegerConverter());

        bean.registerKeyThrowingWhenNullSource(NcipSettingKeys.NCIP_CLIENT_AUTHENTICATION_REQUIRED, StringToBooleanConverter.notAllowingNullSource());
        bean.registerKeyThrowingWhenNullSource(NcipSettingKeys.NCIP_USER_IDENTIFIER, new EToEConverter<>());

        //BAKALARI
        bean.registerKeyThrowingWhenNullSource(BakalariSettingKeys.BAKALARI_ENABLED, StringToBooleanConverter.notAllowingNullSource());
        bean.registerKeyReturningNullWhenNullSource(BakalariSettingKeys.BAKALARI_API_URL, new EToEConverter<>());
        bean.registerKeyReturningNullWhenNullSource(BakalariSettingKeys.BAKALARI_API_USER, new EToEConverter<>());
        bean.registerKeyReturningNullWhenNullSource(BakalariSettingKeys.BAKALARI_API_PASSWORD, new EToEConverter<>());
        bean.registerKeyReturningNullWhenNullSource(BakalariSettingKeys.BAKALARI_CTX_PREFIX, new EToEConverter<>());
        bean.registerKeyReturningNullWhenNullSource(BakalariSettingKeys.BAKALARI_STUDENT_READER_CATEGORY, new EToEConverter<>());
        bean.registerKeyReturningNullWhenNullSource(BakalariSettingKeys.BAKALARI_TEACHER_READER_CATEGORY, new EToEConverter<>());
        bean.registerKeyThrowingWhenNullSource(BakalariSettingKeys.BAKALARI_SYNC_BY_FULLNAME_ENABLED, StringToBooleanConverter.notAllowingNullSource());

        //ALIVE
        bean.registerKeyThrowingWhenNullSource(AliveSettingKeys.ALIVE_VERIFY_ENABLED, StringToBooleanConverter.notAllowingNullSource());
        bean.registerKeyReturningNullWhenNullSource(AliveSettingKeys.ALIVE_VERIFY_API_URL, new EToEConverter<>());
        bean.registerKeyReturningNullWhenNullSource(AliveSettingKeys.ALIVE_VERIFY_API_USER, new EToEConverter<>());
        bean.registerKeyReturningNullWhenNullSource(AliveSettingKeys.ALIVE_VERIFY_API_PASSWORD, new EToEConverter<>());
        bean.registerKeyReturningNullWhenNullSource(AliveSettingKeys.ALIVE_VERIFY_DISCOUNT_ID, new StringToIntegerConverter());

        //EDOOKIT
        bean.registerKeyThrowingWhenNullSource(EdookitSettingKeys.EDOOKIT_ENABLED, StringToBooleanConverter.notAllowingNullSource());
        bean.registerKeyReturningNullWhenNullSource(EdookitSettingKeys.EDOOKIT_API_URL, new EToEConverter<>());
        bean.registerKeyReturningNullWhenNullSource(EdookitSettingKeys.EDOOKIT_API_USER, new EToEConverter<>());
        bean.registerKeyReturningNullWhenNullSource(EdookitSettingKeys.EDOOKIT_API_PASSWORD, new EToEConverter<>());
        bean.registerKeyReturningNullWhenNullSource(EdookitSettingKeys.EDOOKIT_CTX_PREFIX, new EToEConverter<>());
        bean.registerKeyReturningNullWhenNullSource(EdookitSettingKeys.EDOOKIT_STUDENT_READER_CATEGORY, new EToEConverter<>());
        bean.registerKeyReturningNullWhenNullSource(EdookitSettingKeys.EDOOKIT_TEACHER_READER_CATEGORY, new EToEConverter<>());
        bean.registerKeyThrowingWhenNullSource(EdookitSettingKeys.EDOOKIT_EMAIL_GENERATOR_ENABLED, StringToBooleanConverter.notAllowingNullSource());
        bean.registerKeyReturningNullWhenNullSource(EdookitSettingKeys.EDOOKIT_EMAIL_DOMAIN, new EToEConverter<>());

        //EDUPAGE
        bean.registerKeyThrowingWhenNullSource(EdupageSettingKeys.EDUPAGE_ENABLED, StringToBooleanConverter.notAllowingNullSource());
        bean.registerKeyReturningNullWhenNullSource(EdupageSettingKeys.EDUPAGE_API_KEY, new EToEConverter<>());
        bean.registerKeyReturningNullWhenNullSource(EdupageSettingKeys.EDUPAGE_API_URL, new EToEConverter<>());
        bean.registerKeyReturningNullWhenNullSource(EdupageSettingKeys.EDUPAGE_CTX_PREFIX, new EToEConverter<>());
        bean.registerKeyReturningNullWhenNullSource(EdupageSettingKeys.EDUPAGE_STUDENT_READER_CATEGORY, new EToEConverter<>());
        bean.registerKeyReturningNullWhenNullSource(EdupageSettingKeys.EDUPAGE_TEACHER_READER_CATEGORY, new EToEConverter<>());
        bean.registerKeyThrowingWhenNullSource(EdupageSettingKeys.EDUPAGE_DEDUPLICATION_BY_BAKALARI_ID_ENABLED, StringToBooleanConverter.notAllowingNullSource());

        //SOL
        bean.registerKeyThrowingWhenNullSource(SolSettingKeys.SOL_ENABLED, StringToBooleanConverter.notAllowingNullSource());
        bean.registerKeyReturningNullWhenNullSource(SolSettingKeys.SOL_API_URL, new EToEConverter<>());
        bean.registerKeyReturningNullWhenNullSource(SolSettingKeys.SOL_API_USER, new EToEConverter<>());
        bean.registerKeyReturningNullWhenNullSource(SolSettingKeys.SOL_API_PASSWORD, new EToEConverter<>());
        bean.registerKeyReturningNullWhenNullSource(SolSettingKeys.SOL_STUDENT_READER_CATEGORY, new EToEConverter<>());
        bean.registerKeyReturningNullWhenNullSource(SolSettingKeys.SOL_TEACHER_READER_CATEGORY, new EToEConverter<>());
        bean.registerKeyThrowingWhenNullSource(SolSettingKeys.SOL_NAME_SYNCHRONIZATION_ENABLED, StringToBooleanConverter.notAllowingNullSource());

        //SUTIN
        bean.registerKeyThrowingWhenNullSource(SutinSettingKeys.SUTIN_ENABLED, StringToBooleanConverter.notAllowingNullSource());
        bean.registerKeyReturningNullWhenNullSource(SutinSettingKeys.SUTIN_DB_CONNECTION_STRING, new EToEConverter<>());
        bean.registerKeyReturningNullWhenNullSource(SutinSettingKeys.SUTIN_DB_USER, new EToEConverter<>());
        bean.registerKeyReturningNullWhenNullSource(SutinSettingKeys.SUTIN_DB_PASSWORD, new EToEConverter<>());

        //POWERKEY
        bean.registerKeyThrowingWhenNullSource(PowerkeySettingKeys.POWERKEY_ENABLED, StringToBooleanConverter.notAllowingNullSource());
        bean.registerKeyReturningNullWhenNullSource(PowerkeySettingKeys.POWERKEY_DB_CONNECTION_STRING, new EToEConverter<>());
        bean.registerKeyReturningNullWhenNullSource(PowerkeySettingKeys.POWERKEY_DB_USER, new EToEConverter<>());
        bean.registerKeyReturningNullWhenNullSource(PowerkeySettingKeys.POWERKEY_DB_PASSWORD, new EToEConverter<>());

        //UNIS
        bean.registerKeyThrowingWhenNullSource(UnisSettingKeys.UNIS_ENABLED, StringToBooleanConverter.notAllowingNullSource());
        bean.registerKeyReturningNullWhenNullSource(UnisSettingKeys.UNIS_API_URL, new EToEConverter<>());
        bean.registerKeyReturningNullWhenNullSource(UnisSettingKeys.UNIS_API_USER, new EToEConverter<>());
        bean.registerKeyReturningNullWhenNullSource(UnisSettingKeys.UNIS_API_PASSWORD, new EToEConverter<>());

        //UNOB
        bean.registerKeyThrowingWhenNullSource(UnobSettingKeys.UNOB_ENABLED, StringToBooleanConverter.notAllowingNullSource());
        bean.registerKeyReturningNullWhenNullSource(UnobSettingKeys.UNOB_API_URL, new EToEConverter<>());
        bean.registerKeyReturningNullWhenNullSource(UnobSettingKeys.UNOB_API_USER, new EToEConverter<>());
        bean.registerKeyReturningNullWhenNullSource(UnobSettingKeys.UNOB_API_PASSWORD, new EToEConverter<>());
        bean.registerKeyReturningNullWhenNullSource(UnobSettingKeys.UNOB_API_KEY, new EToEConverter<>());

        //CPK
        bean.registerKeyThrowingWhenNullSource(CpkSettingKeys.API_ENABLED, StringToBooleanConverter.notAllowingNullSource());
        bean.registerKeyReturningNullWhenNullSource(CpkSettingKeys.BASE_URL, new EToEConverter<>());
        bean.registerKeyReturningNullWhenNullSource(CpkSettingKeys.INSTITUTION_KEY, new EToEConverter<>());

        //ZISKEJ
        bean.registerKeyThrowingWhenNullSource(ZiskejSettingKeys.ENABLED, StringToBooleanConverter.notAllowingNullSource());
        bean.registerKeyThrowingWhenNullSource(ZiskejSettingKeys.SEEKING_ENABLED, StringToBooleanConverter.notAllowingNullSource());
        bean.registerKeyThrowingWhenNullSource(ZiskejSettingKeys.PROVISION_ENABLED, StringToBooleanConverter.notAllowingNullSource());
        bean.registerKeyThrowingWhenNullSource(ZiskejSettingKeys.WEB_URL, new EToEConverter<>());
        bean.registerKeyThrowingWhenNullSource(ZiskejSettingKeys.API_URL, new EToEConverter<>());
        bean.registerKeyReturningNullWhenNullSource(ZiskejSettingKeys.API_PASSWORD, new EToEConverter<>());

        //REPORT SERVER
        bean.registerKeyThrowingWhenNullSource(ReportServerSettingsKeys.REPORT_SERVER_ENABLED, StringToBooleanConverter.notAllowingNullSource());
        bean.registerKeyReturningNullWhenNullSource(ReportServerSettingsKeys.REPORT_SERVER_API_URL, new EToEConverter<>());
        bean.registerKeyReturningNullWhenNullSource(ReportServerSettingsKeys.REPORT_SERVER_READER_CLIENT_ID, new EToEConverter<>());
        bean.registerKeyReturningNullWhenNullSource(ReportServerSettingsKeys.REPORT_SERVER_READER_CLIENT_TOKEN, new EToEConverter<>());
        bean.registerKeyReturningNullWhenNullSource(ReportServerSettingsKeys.REPORT_SERVER_ROOT_FOLDER, new EToEConverter<>());
        bean.registerKeyReturningNullWhenNullSource(ReportServerSettingsKeys.REPORT_SERVER_EDITOR_CLIENT_ID, new EToEConverter<>());
        bean.registerKeyReturningNullWhenNullSource(ReportServerSettingsKeys.REPORT_SERVER_EDITOR_CLIENT_TOKEN, new EToEConverter<>());
        bean.registerKeyReturningNullWhenNullSource(ReportServerSettingsKeys.REPORT_SERVER_RECORD_ROOT_FOLDER, new EToEConverter<>());

        //SIP2
        bean.registerKeyThrowingWhenNullSource(Sip2ServerSettingKeys.SIP2_ENABLED, StringToBooleanConverter.notAllowingNullSource());
        bean.registerKeyThrowingWhenNullSource(Sip2ServerSettingKeys.SIP2_PORT, new StringToIntegerConverter());
        bean.registerKeyReturningNullWhenNullSource(Sip2ServerSettingKeys.SIP2_TERMINAL_PASSWORD, new EToEConverter<>());
        bean.registerKeyReturningNullWhenNullSource(Sip2ServerSettingKeys.SIP2_CONTEXT, new StringToIntegerConverter());
        bean.registerKeyReturningNullWhenNullSource(Sip2ServerSettingKeys.SIP2_INSTITUTION_ID, new EToEConverter<>());
        bean.registerKeyThrowingWhenNullSource(Sip2ServerSettingKeys.SIP2_USER_IDENTIFIER, new EToEConverter<>());
        bean.registerKeyThrowingWhenNullSource(Sip2ServerSettingKeys.SIP2_STRICT_USER_IDENTIFIER_ENABLED, StringToBooleanConverter.notAllowingNullSource());
        bean.registerKeyThrowingWhenNullSource(Sip2ServerSettingKeys.SIP2_BOOKED_RECORDS_RETURNING_ENABLED, StringToBooleanConverter.notAllowingNullSource());
        bean.registerKeyThrowingWhenNullSource(Sip2ServerSettingKeys.SIP2_SORT_BIN_PROCEDURE_ENABLED, StringToBooleanConverter.notAllowingNullSource());
        bean.registerKeyThrowingWhenNullSource(Sip2ServerSettingKeys.SIP2_FORBIDDEN_LENDING_READER_CATEGORIES, new StringToStringListConverter());
        bean.registerKeyThrowingWhenNullSource(Sip2ServerSettingKeys.SIP2_ALLOWED_LENDING_LOCATIONS_ENABLED, StringToBooleanConverter.notAllowingNullSource());
        bean.registerKeyThrowingWhenNullSource(Sip2ServerSettingKeys.SIP2_ALLOWED_LENDING_LOCATIONS, new StringToIntegerListConverter());
        bean.registerKeyThrowingWhenNullSource(Sip2ServerSettingKeys.SIP2_FORBIDDEN_LENDING_LOCATIONS, new StringToIntegerListConverter());
        bean.registerKeyThrowingWhenNullSource(Sip2ServerSettingKeys.SIP2_FORBIDDEN_LOAN_CATEGORIES, new StringToStringListConverter());
        bean.registerKeyThrowingWhenNullSource(Sip2ServerSettingKeys.SIP2_LENDING_EXEMPLAR_MAX_PRICE_ENABLED, StringToBooleanConverter.notAllowingNullSource());
        bean.registerKeyReturningNullWhenNullSource(Sip2ServerSettingKeys.SIP2_LENDING_EXEMPLAR_MAX_PRICE, new StringToIntegerConverter());
        bean.registerKeyThrowingWhenNullSource(Sip2ServerSettingKeys.SIP2_ALLOWED_LENDING_EXEMPLAR_STATUSES_ENABLED, StringToBooleanConverter.notAllowingNullSource());
        bean.registerKeyThrowingWhenNullSource(Sip2ServerSettingKeys.SIP2_ALLOWED_LENDING_EXEMPLAR_STATUSES, new StringToIntegerListConverter());
        bean.registerKeyThrowingWhenNullSource(Sip2ServerSettingKeys.SIP2_HOLD_PATRON_ID_IN_ITEM_PROPERTIES_ENABLED, StringToBooleanConverter.notAllowingNullSource());
        bean.registerKeyThrowingWhenNullSource(Sip2ServerSettingKeys.SIP2_LOAN_MEDIUM_IN_ITEM_PROPERTIES_ENABLED, StringToBooleanConverter.notAllowingNullSource());
        bean.registerKeyThrowingWhenNullSource(Sip2ServerSettingKeys.SIP2_PATRON_INFORMATION_TRIGGER_RENEWAL_ENABLED, StringToBooleanConverter.notAllowingNullSource());

        //VERBISBOXER
        bean.registerKeyThrowingWhenNullSource(VerbisboxerManagerApiSettingKeys.VERBISBOXER_ENABLED, StringToBooleanConverter.notAllowingNullSource());
        bean.registerKeyThrowingWhenNullSource(VerbisboxerManagerApiSettingKeys.VERBISBOXER_ON_THE_FLY_LOANS_ENABLED, StringToBooleanConverter.notAllowingNullSource());
        bean.registerKeyThrowingWhenNullSource(VerbisboxerManagerApiSettingKeys.VERBISBOXER_ON_THE_FLY_RETURNS_ENABLED, StringToBooleanConverter.notAllowingNullSource());
        bean.registerKeyThrowingWhenNullSource(VerbisboxerManagerApiSettingKeys.VERBISBOXER_API_URL, new EToEConverter<>());
        bean.registerKeyThrowingWhenNullSource(VerbisboxerManagerApiSettingKeys.VERBISBOXER_API_TOKEN_URL, new EToEConverter<>());
        bean.registerKeyReturningNullWhenNullSource(VerbisboxerManagerApiSettingKeys.VERBISBOXER_API_CLIENT_ID, new EToEConverter<>());
        bean.registerKeyReturningNullWhenNullSource(VerbisboxerManagerApiSettingKeys.VERBISBOXER_API_CLIENT_SECRET, new EToEConverter<>());

        bean.registerKeyThrowingWhenNullSource(IfisSettingKeys.IFIS_ENABLED, StringToBooleanConverter.notAllowingNullSource());
        bean.registerKeyReturningNullWhenNullSource(IfisSettingKeys.IFIS_SOAP_URL, new EToEConverter<>());
        bean.registerKeyReturningNullWhenNullSource(IfisSettingKeys.IFIS_SOAP_ENVELOPE_USERNAME, new EToEConverter<>());
        bean.registerKeyReturningNullWhenNullSource(IfisSettingKeys.IFIS_SOAP_ENVELOPE_PASSWORD, new EToEConverter<>());

        //GUI
        bean.registerKeyThrowingWhenNullSource(CatalogWebSettingsKeys.HEADER_BACKGROUND_COLOR, new EToEConverter<>());
        bean.registerKeyThrowingWhenNullSource(CatalogWebSettingsKeys.HEADER_TEXT_COLOR, new EToEConverter<>());
        bean.registerKeyThrowingWhenNullSource(CatalogWebSettingsKeys.HEADER_LINK_COLOR, new EToEConverter<>());
        bean.registerKeyThrowingWhenNullSource(CatalogWebSettingsKeys.MAIN_MENU_BACKGROUND_COLOR, new EToEConverter<>());
        bean.registerKeyThrowingWhenNullSource(CatalogWebSettingsKeys.MAIN_MENU_TEXT_COLOR, new EToEConverter<>());
        bean.registerKeyThrowingWhenNullSource(CatalogWebSettingsKeys.GLOBAL_SEARCH_BUTTON_COLOR, new EToEConverter<>());
        bean.registerKeyThrowingWhenNullSource(CatalogWebSettingsKeys.TABLE_HEADER_ACCENT_COLOR, new EToEConverter<>());
        bean.registerKeyThrowingWhenNullSource(CatalogWebSettingsKeys.SELECTED_TAB_HIGHLIGHT_COLOR, new EToEConverter<>());
        bean.registerKeyThrowingWhenNullSource(CatalogWebSettingsKeys.SUB_SEARCH_LINKS, HtmlLink.createConverter());
        bean.registerKeyThrowingWhenNullSource(CatalogWebSettingsKeys.EXEMPLARS, new StringToIntegerConverter());
        bean.registerKeyThrowingWhenNullSource(CatalogWebSettingsKeys.ARTICLES, new StringToIntegerConverter());
        bean.registerKeyThrowingWhenNullSource(CatalogWebSettingsKeys.PARTS, new StringToIntegerConverter());
        bean.registerKeyThrowingWhenNullSource(CatalogWebSettingsKeys.CITATION, new StringToIntegerConverter());
        bean.registerKeyThrowingWhenNullSource(CatalogWebSettingsKeys.NONE, new StringToIntegerConverter());
        bean.registerKeyThrowingWhenNullSource(CatalogWebSettingsKeys.TOC, new StringToIntegerConverter());
        bean.registerKeyThrowingWhenNullSource(CatalogWebSettingsKeys.DETAIL, new StringToIntegerConverter());
        bean.registerKeyThrowingWhenNullSource(CatalogWebSettingsKeys.MARC, new StringToIntegerConverter());
        bean.registerKeyThrowingWhenNullSource(CatalogWebSettingsKeys.LOANS, new StringToIntegerConverter());
        bean.registerKeyThrowingWhenNullSource(CatalogWebSettingsKeys.OPERATIONS, new StringToIntegerConverter());
        bean.registerKeyReturningNullWhenNullSource(CatalogWebSettingsKeys.MAIN_MENU_HIGHLIGHT_TEXT_COLOR, new EToEConverter<>());
        bean.registerKeyReturningNullWhenNullSource(CatalogWebSettingsKeys.MAIN_MENU_HIGHLIGHT_BACKGROUND_COLOR, new EToEConverter<>());
        bean.registerKeyThrowingWhenNullSource(CatalogWebSettingsKeys.SUTOR_SUTIN_LAYOUT_ENABLED, StringToBooleanConverter.notAllowingNullSource());
        bean.registerKeyThrowingWhenNullSource(CatalogWebSettingsKeys.LAYOUT_USER_ACCOUNT, GenericLayoutSettingHolder.createConverter());
        bean.registerKeyThrowingWhenNullSource(CatalogWebSettingsKeys.LAYOUT_DOCUMENT_DETAIL, GenericLayoutSettingHolder.createConverter());
        bean.registerKeyThrowingWhenNullSource(CatalogWebSettingsKeys.LAYOUT_AUTHORITY_DETAIL, GenericLayoutSettingHolder.createConverter());
        bean.registerKeyReturningNullWhenNullSource(CatalogWebSettingsKeys.LAYOUT_RECORD_DETAIL, GenericLayoutSettingHolder.createConverter());

        // DATABASE BACKUP
        bean.registerKeyThrowingWhenNullSource(DbBackupSettingKeys.BACKUP_POSTGRES_ENABLED, StringToBooleanConverter.notAllowingNullSource());
        bean.registerKeyReturningNullWhenNullSource(DbBackupSettingKeys.BACKUP_POSTGRES_UZIV, new EToEConverter<>());
        bean.registerKeyThrowingWhenNullSource(DbBackupSettingKeys.BACKUP_POSTGRES_BLOBS, StringToBooleanConverter.notAllowingNullSource());

        // BARCODE CARDS
        bean.registerKeyThrowingWhenNullSource(CatalogWebSettingsKeys.BARCODE_CARDS_ENABLED, StringToBooleanConverter.notAllowingNullSource());
        bean.registerKeyThrowingWhenNullSource(CatalogWebSettingsKeys.BARCODE_CARD_BACKGROUND_COLOR, new EToEConverter<>());
        bean.registerKeyThrowingWhenNullSource(CatalogWebSettingsKeys.BARCODE_CARD_TEXT_COLOR, new EToEConverter<>());
        bean.registerKeyThrowingWhenNullSource(CatalogWebSettingsKeys.BARCODE_CARD_LABEL_COLOR, new EToEConverter<>());

        return bean;
    }

    @Bean
    public PermissionRegistry permissionRegistry() {
        return new PermissionRegistry();
    }

    @Bean
    public SecurityManager securityManager() {
        return new PermissionRegistrySecurityManager(permissionRegistry());
    }

    @Bean
    public ServerUrlConfiguration serverUrlConfiguration() {
        return new ServerUrlConfiguration(
                rootServerUrlProvider(),
                publicContextPath(),
                contextPath(),
                settingLoader().getOnRootProvider(SERVER_REVERSE_PROXY),
                settingLoader().getOnRootProvider(FORCE_HTTPS_ENABLED));
    }

    @Bean
    public Provider<@NonNull String> publicContextPath() {
        Provider<String> nullablePublicContextPathProvider = rootServerUrlProvider().andThenFastReturningNull(new ServerUrlToPublicContextPathConverter()::convert);
        return FallbackedProvider.ofStaticFallback(nullablePublicContextPathProvider, contextPath()).fallbackOnException().fallbackOnNull();
    }

    /**
     * Bean declaration to be @Cacheable
     */
    @Bean
    public RequestUrlCurrentDepartmentWebResolver.DepartmentByRequestUrlBreadthFirstSearchProvider departmentByRequestUrlBreadthFirstSearchProvider() {
        RequestUrlCurrentDepartmentWebResolver.DepartmentByRequestUrlBreadthFirstSearchProvider bean = new RequestUrlCurrentDepartmentWebResolver.DepartmentByRequestUrlBreadthFirstSearchProvider(
                rootDepartmentProvider(),
                departmentAccessor(),
                serverUrlProvider()
        );
        cacheService.registerSpringCacheCleaner(RequestUrlCurrentDepartmentWebResolver.DepartmentByRequestUrlBreadthFirstSearchProvider.CACHE_NAME);
        return bean;
    }

    @Bean
    public WebResolver<Department> currentDepartmentWebResolver() {
        return new CompositeCurrentDepartmentWebResolver(List.of(
                new CookieValueWebResolver(CookieCurrentDepartmentSaver.CURRENT_BUILDING_COOKIE_NAME).thenConverting(StringToIntegerToAnyConverter.nullConvertingToNull(new IdToObjectConverter<>(departmentLoader()))),
                new HttpHeaderValueWebResolver(CatalogWebConstants.CURRENT_DEPARTMENT_HEADER_NAME).thenConverting(StringToIntegerToAnyConverter.nullConvertingToNull(new IdToObjectConverter<>(departmentLoader()))),
                new RequestUrlCurrentDepartmentWebResolver(departmentByRequestUrlBreadthFirstSearchProvider()),
                new ProvidedWebResolver<>(rootDepartmentProvider())
        ));
    }

    @Bean
    public CurrentDepartmentSaver currentDepartmentSaver() {
        return new CookieCurrentDepartmentSaver();
    }

    @Bean
    @Scope(value = WebApplicationContext.SCOPE_REQUEST, proxyMode = ScopedProxyMode.INTERFACES)
    public Provider<Department> currentDepartmentProvider() {
        return new CurrentDepartmentProvider(currentDepartmentWebResolver(), httpServletRequest);
    }

    @Bean
    public WebResolver<BytesRange> bytesRangeWebResolver() {
        return new BytesRangeWebResolver(new HttpHeaderValueWebResolver(HttpHeaderConstants.Range.NAME));
    }

    @Bean
    public ExecutorService executorService() {
        return Executors.newCachedThreadPool();
    }

    @Bean
    public MessageSource messageSource() {
        return new TranslatorDelegatingMessageSource<>(
                translator(),
                rootDepartmentProvider());
    }

    @Bean
    public Translator<Department> translator() {
        return new HierarchyTraversingTranslator<>(
                messageTranslationsLoader(),
                departmentAccessor()::getThisAndParentsChain,
                new FallbackLocaleResolver()
                        .add(Locales.CS, Locales.SK, Locales.EN)
                        .add(Locales.SK, Locales.CS, Locales.EN)
                        .add(Locales.FR, Locales.EN)
                        .addDefaultFallback(settingLoader().getOnRootProvider(DEFAULT_LOCALE)), // this fallback locale should be EN, but not all custom message codes are always translated to EN - only czech
                Locales.XX
        );
    }

    @Bean
    public ContextualLocaleLocalizer<Department> localizer() {
        return new ContextualLocaleLocalizer<>(translator(), settingLoader().getDepartmentedProvider(DEFAULT_LOCALE));
    }

    @Bean
    public AllValuesProvider<Localization> localizationLoader() {
        return new SpringDbLocalizationLoader(departmentLoader(), jdbcTemplate, queryFactory, stringToLocaleConverter());
    }

    @Bean
    public Provider<Map<LocalizationId, MessageTranslations>> messageTranslationsLoader() {
        var cachedProvider = new LocalizationLoaderDelegatingMessageTranslationsLoader(localizationLoader())
                .cached();
        cacheService.registerCleaner(MessageFormat.class.getSimpleName(), cachedProvider);
        return cachedProvider;
    }

    @Bean
    public Converter<String, Locale> stringToLocaleConverter() {
        return new StringToLocaleConverter();
    }

    @Bean
    public NumberFormat currencyFormat() {
        return NumberFormat.getCurrencyInstance(CURRENCY_CZK_LOCALE);
    }

    @Bean
    public ContextualProvider<Department, @NullableNotBlank String> nullableSiglaProvider() {
        return HierarchicalContextualProvider.ofGetterReturnsNonNullValue(
                HierarchyFinderDepartmentedFactory.ofDepartment(departmentLoader()),
                Department::getSigla
        );
    }

    @Bean
    public ContextualProvider<Department, @NonNull String> siglaProvider() {
        return nullableSiglaProvider().throwingWhenNull(ctx -> new MissingValueException("Sigla of department %s is not set".formatted(ctx)));
    }

    @Bean
    public Provider<@NonNull List<@NonNull String>> allSiglasProvider() {
        var provider = Provider.of(() ->
                departmentLoader().getAll().stream().map(Department::getSigla).toList()
        ).cached();
        cacheService.registerCleaner(Department.class.getSimpleName(), provider);
        return provider;
    }

    @Bean
    public ContextualProvider<Department, @NonNull SystemInstitution> systemInstitutionProvider() {
        ContextualProvider<Department, String> libraryName = settingLoader().getDepartmentedProvider(LIBRARY_NAME).andThenFastReturningNull(StringUtil::notBlankTrimmedString);
        ContextualProvider<Department, String> libraryStreet = settingLoader().getDepartmentedProvider(LIBRARY_STREET).andThenFastReturningNull(StringUtil::notBlankTrimmedString);
        ContextualProvider<Department, String> libraryPostalCode = settingLoader().getDepartmentedProvider(LIBRARY_POSTAL_CODE).andThenFastReturningNull(StringUtil::notBlankTrimmedString);
        ContextualProvider<Department, String> libraryCity = settingLoader().getDepartmentedProvider(LIBRARY_CITY).andThenFastReturningNull(StringUtil::notBlankTrimmedString);
        ContextualProvider<Department, String> libraryEmail = settingLoader().getDepartmentedProvider(LIBRARY_EMAIL).andThenFastReturningNull(StringUtil::notBlankTrimmedString);
        ContextualProvider<Department, String> libraryPhoneNumber = settingLoader().getDepartmentedProvider(LIBRARY_PHONE_NUMBER).andThenFastReturningNull(StringUtil::notBlankTrimmedString);
        ContextualProvider<Department, String> libraryCin = settingLoader().getDepartmentedProvider(LIBRARY_CIN).andThenFastReturningNull(StringUtil::notBlankTrimmedString);
        ContextualProvider<Department, String> libraryVatin = settingLoader().getDepartmentedProvider(LIBRARY_VATIN).andThenFastReturningNull(StringUtil::notBlankTrimmedString);
        return ctx -> new SystemInstitution(
                libraryName.getOn(ctx),
                libraryStreet.getOn(ctx),
                libraryPostalCode.getOn(ctx),
                libraryCity.getOn(ctx),
                libraryEmail.getOn(ctx),
                libraryPhoneNumber.getOn(ctx),
                libraryCin.getOn(ctx),
                libraryVatin.getOn(ctx)
        );
    }

    @Bean
    public Provider<@NonNull String> jwtSymetricKeySecretProvider() {
        var kpsysApiKeyProvider = settingLoader().getOnRootProvider(SettingKeys.KPSYS_API_KEY).throwingWhenNull();
        // key is used in HmacSHA256 algorithm, so must be at least 32 chars (= 256 bits) long
        return () -> StringUtil.appendToSize(kpsysApiKeyProvider.get(), '0', 32);
    }

    @Bean
    public MessageCryptor messageCryptor() {
        return MessageCryptor.createWithAesCbcPkcs5Padding(jwtSymetricKeySecretProvider());
    }

    @Bean
    public ActionRequestUrlGenerator actionRequestUrlGenerator() {
        return new ActionRequestUrlGenerator(
                serverUrlProvider().throwingWhenNull(),
                objectMapper);
    }

    @Bean
    public Mailer<Department> mailer() {
        ContextualProvider<Department, JavaMailSender> mailSenderFactory = new JavaMailSenderFactory<>(
                settingLoader().getDepartmentedProvider(SMTP_TRANSPORT_STRATEGY).throwingWhenNull(),
                settingLoader().getDepartmentedProvider(SMTP_SERVER_ADDRESS).throwingWhenNull(),
                settingLoader().getDepartmentedProvider(SMTP_SERVER_PORT),
                settingLoader().getDepartmentedProvider(SMTP_AUTHENTICATION_ENABLED).throwingWhenNull(),
                settingLoader().getDepartmentedProvider(SMTP_SERVER_USERNAME),
                settingLoader().getDepartmentedProvider(SMTP_SERVER_PASSWORD),
                ContextIgnoringContextualProvider.of(false)
        );

        ContextualProvider<Department, JavaMailSender> cachedMailSenderFactory = new TemporarilyCachedContextualProvider<>(mailSenderFactory, Duration.ofMinutes(1));

        JavaMailSenderMailer<Department> pureMailer = new JavaMailSenderMailer<>(
                settingLoader().getDepartmentedProvider(SettingKeys.SMTP_SENDER_ADDRESS),
                settingLoader().getDepartmentedProvider(SettingKeys.SMTP_SENDER_NAME),
                settingLoader().getDepartmentedProvider(SMTP_USE_SENDER_AND_FROM),
                settingLoader().getDepartmentedProvider(SMTP_FROM_LIBRARY_MAILS_REPLY_TO_ADDRESS),
                cachedMailSenderFactory
        );
        return new AttachmentsZippingMailer<>(3, pureMailer, new FileZipperImpl());
    }


    @Bean
    public ActionSaver actionSaver() {
        return new SpringDbActionSaver(jdbcTemplate, queryFactory);
    }


    @Bean
    public IpAddressInRangeResolver internalIpResolver() {
        return new CombinedIpAddressRange(
                new ListedIpAddressRange(settingLoader().getOnRootProvidedList(SettingKeys.EXTERNAL_IP_ADDRESSES)),
                new ListedIpAddressRange(settingLoader().getOnRootProvidedList(SettingKeys.INTERNAL_IP_ADDRESSES)),
                settingLoader().getOnRootProvider(SettingKeys.INTERNAL_IP_ADDRESS_RANGE)
        );
    }

    @Bean
    public AppserverService authorizedUserAddingAppserverService() {
        return new UserAddingAppserverService(authorizedAppserverService, authenticationHolder());
    }

    @Bean
    public MappingAppserverService mappingAppserver() {
        return new BasicMappingAppserverService(authorizedUserAddingAppserverService());
    }

    @Bean
    public DmlAppserverService dmlAppserverService() {
        return new MappingAppserverDmlAppserverService(mappingAppserver());
    }

    @Bean
    public LocalizationsSaver localizationsSaver() {
        return new LocalizationsSaverAppserver(
                dmlAppserverService(),
                cacheService.createCleanerFor(MessageFormat.class.getSimpleName()));
    }

    @Bean
    public ModelBeanBuilder modelBeanBuilder() {
        return new ModelBeanBuilder(
                entityManager,
                cacheService,
                dmlAppserverService(),
                searchLoaderBuilderFactory()
        );
    }

    @Bean
    public SearchLoaderBuilderFactory searchLoaderBuilderFactory() {
        return new SearchLoaderBuilderFactory();
    }

    @Bean
    public SaverBuilderFactory saverBuilderFactory() {
        return new SaverBuilderFactory(dmlAppserverService(), entityManager, cacheService);
    }

    @Bean
    public BarCodeValidatorByBarCodeTypeProvider barCodeValidatorByBarCodeTypeProvider() {
        var provider = new BarCodeValidatorByBarCodeTypeProvider();
        return provider
                .registerValidator(BarCodeType.EAN_8, EanBarcodeValidator.EAN8_VALIDATOR)
                .registerValidator(BarCodeType.EAN_13, EanBarcodeValidator.EAN13_VALIDATOR)
                .registerValidator(BarCodeType.CODE_39, new Code39BarcodeValidator(settingLoader().getOnRootProvider(UserSettingKeys.USER_BARCODE_CORE_MAX_LENGTH)))
                .registerValidator(BarCodeType.ITF_18, new ItfBarcodeValidator(StaticProvider.of(18)));
    }
}
