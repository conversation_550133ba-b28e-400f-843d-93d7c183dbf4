{"name": "portaro", "version": "2.3.0", "description": "Portaro Library Catalog", "homepage": "https://verbis.io/", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "<PERSON>", "email": "tomas.<PERSON><PERSON>@systemist.cz"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "pavel.k<PERSON><PERSON><PERSON>@systemist.cz"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "license": "ISC", "dependencies": {"@babel/runtime": "^7.17.2", "@dagrejs/dagre": "^1.1.4", "@fingerprintjs/botd": "^1.3.2", "@floating-ui/core": "^1.6.8", "@googlemaps/js-api-loader": "^1.16.8", "@googlemaps/markerclusterer": "^2.5.3", "@kpsys/angularjs-register": "^1.1.4", "@rgossiaux/svelte-headlessui": "^2.0.0", "@tanstack/svelte-table": "^8.15.3", "@trevoreyre/autocomplete": "^3.0.3", "@uirouter/angularjs": "^1.0.28", "@xyflow/svelte": "^0.1.25", "@zxing/library": "^0.20.0", "ace-builds": "^1.36.4", "angular": "^1.8.2", "angular-sanitize": "^1.8.2", "angular-slick-carousel": "^3.1.7", "bootstrap-less": "3.3.8", "cl-editor": "^2.3.0", "core-js": "3.39.0", "html-to-image": "^1.11.13", "jsbarcode": "^3.11.6", "lodash-es": "^4.17.21", "luxon": "^3.2.1", "marked": "^15.0.11", "minidenticons": "^4.2.1", "ng-file-upload": "^12.2.13", "pdfjs-dist": "^4.8.69", "rxjs": "^7.5.4", "slick-carousel": "^1.6.0", "svelte": "4.2.19", "svelte-chartjs": "^3.1.5", "svelte-dnd-action": "^0.9.61", "svelte-floating-ui": "^1.5.9", "svelte-forms": "^2.3.1", "svelte-multiselect": "^10.3.0", "svelte-render": "^2.0.1", "svelte-virtual-scroll-list": "^1.0.8", "svelty-picker": "^5.2.11", "tippy.js": "^6.3.7", "tslib": "^2.5.2"}, "devDependencies": {"@babel/core": "^7.18.6", "@babel/plugin-transform-runtime": "^7.17.0", "@babel/preset-env": "^7.16.11", "@eslint/compat": "^1.2.2", "@eslint/eslintrc": "^3.1.0", "@eslint/js": "^9.14.0", "@storybook/addon-a11y": "^8.4.2", "@storybook/addon-actions": "^8.4.2", "@storybook/addon-essentials": "^8.4.2", "@storybook/addon-interactions": "^8.4.2", "@storybook/addon-links": "^8.4.2", "@storybook/addon-svelte-csf": "^4.1.7", "@storybook/blocks": "^8.4.2", "@storybook/svelte": "^8.4.2", "@storybook/svelte-webpack5": "^8.4.2", "@storybook/test": "^8.4.2", "@stylistic/eslint-plugin": "^2.10.1", "@testing-library/svelte": "4.1.0", "@types/angular": "^1.7.3", "@types/angular-mocks": "^1.7.0", "@types/google.maps": "^3.58.1", "@types/jasmine": "^5.1.4", "@types/jquery": "^3.5.16", "@types/lodash-es": "^4.17.12", "@types/luxon": "^3.3.0", "@types/node": "20.11.1", "@types/webpack-env": "^1.18.1", "@typescript-eslint/eslint-plugin": "^8.13.0", "@typescript-eslint/parser": "^8.13.0", "angular-mocks": "^1.8.2", "babel-loader": "^9.1.2", "babel-plugin-angularjs-annotate": "^0.10.0", "babel-plugin-istanbul": "^7.0.0", "browserslist": "^4.16.3", "clean-webpack-plugin": "^4.0.0", "cross-env": "^7.0.3", "css-loader": "^7.1.2", "css-minimizer-webpack-plugin": "^7.0.0", "eslint": "^9.14.0", "eslint-plugin-import": "^2.24.0", "eslint-plugin-jsdoc": "^50.4.3", "eslint-plugin-storybook": "^0.11.0", "eslint-plugin-svelte": "^2.35.1", "eslint-plugin-testing-library": "^6.2.0", "eslint-plugin-unicorn": "^56.0.0", "eslint-webpack-plugin": "^4.0.0", "extra-watch-webpack-plugin": "^1.0.3", "file-loader": "^6.1.1", "globals": "^15.12.0", "ip": "^2.0.1", "jasmine": "^5.0.0", "jasmine-core": "^5.0.0", "jquery": "^3.4.1", "karma": "^6.3.16", "karma-chrome-launcher": "^3.1.0", "karma-cli": "^2.0.0", "karma-coverage-istanbul-reporter": "^3.0.3", "karma-firefox-launcher": "^2.1.2", "karma-jasmine": "^5.1.0", "karma-source-map-support": "^1.4.0", "karma-sourcemap-loader": "^0.4.0", "karma-spec-reporter": "^0.0.36", "karma-webpack": "^5.0.0-alpha.5", "less": "^4.2.2", "less-loader": "^12.2.0", "mini-css-extract-plugin": "^2.5.3", "ngtemplate-loader": "^2.1.0", "postcss": "^8.4.21", "regex-replace-loader": "^1.0.2", "sass": "^1.80.6", "sass-loader": "^16.0.3", "simple-ngtemplate-loader": "^2.0.1", "storybook": "^8.4.2", "storybook-less-loader": "^0.1.0", "style-loader": "^4.0.0", "stylelint": "^16.2.1", "stylelint-config-standard": "^36.0.0", "svelte-check": "^4.0.5", "svelte-loader": "^3.1.8", "svelte-preprocess": "^6.0.3", "svg-inline-loader": "^0.8.2", "terser-webpack-plugin": "^5.2.4", "ts-loader": "^9.2.6", "ts-node": "^10.5.0", "typescript": "^5.0.4", "webpack": "^5.96.1", "webpack-cli": "^5.0.1", "webpack-config-dump-plugin": "^3.0.1", "webpack-dev-server": "^5.0.3", "webpack-merge": "^6.0.1", "webpack-shell-plugin-next": "^2.1.1"}, "overrides": {"@rgossiaux/svelte-headlessui": {"svelte": "$svelte"}}, "browserslist": ["last 2 years, not dead"], "scripts": {"build": "cross-env ENVIRONMENT=development webpack --progress", "build:prod": "npm run svelte-check && karma start test/karma.conf.ts && cross-env ENVIRONMENT=production webpack --progress", "build:prod-without-tests": "node -e \"console.error('\\033[0;31mUse this only in very exceptional situation.\\033[0m\\n')\" && cross-env ENVIRONMENT=production webpack --progress", "build:develWithGradle": "cross-env ENVIRONMENT=development-with-gradle webpack --progress", "build:prodWithGradle": "cross-env ENVIRONMENT=production-with-gradle webpack --progress", "profile": "cross-env ENVIRONMENT=development webpack --progress --profile --json > stats.json", "profile:prod": "cross-env ENVIRONMENT=production webpack --progress --profile --json > stats.json", "test": "karma start test/karma.conf.ts", "test:chromium": "karma start test/karma.conf.ts --launcher chromium", "test:firefox": "karma start test/karma.conf.ts --launcher firefox", "gradle": "./../../../../../gradlew processResources -x buildFrontend -p ../../../../", "gradle-win": ".\\..\\..\\..\\..\\..\\gradlew processResources -x buildFrontend -p ../../../../", "svelte-check": "svelte-check --tsconfig ./tsconfig.svelte.json --threshold \"warning\"", "svelte-check:watch": "svelte-check --tsconfig ./tsconfig.svelte.json --threshold \"warning\" --watch", "svelte-check:hints": "svelte-check --tsconfig ./tsconfig.svelte.json", "lint": "eslint src/", "lint:fix": "eslint --fix src/", "storybook": "storybook dev -p 6006", "build-storybook": "storybook build"}}