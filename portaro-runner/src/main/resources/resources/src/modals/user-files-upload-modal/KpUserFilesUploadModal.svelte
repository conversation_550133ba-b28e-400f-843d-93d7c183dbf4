<script lang="ts">
    import type {UserFilesUploadModalModel} from './types';
    import type {ModalWindowActions} from 'shared/modal-dialogs/types';
    import KpModalContent from '../kp-modal/KpModalContent.svelte';
    import KpModalFooterCloseButton from 'src/modals/kp-modal/KpModalFooterCloseButton.svelte';
    import KpModalHeaderCloseButton from 'src/modals/kp-modal/KpModalHeaderCloseButton.svelte';
    import KpModalTitle from 'src/modals/kp-modal/KpModalTitle.svelte';
    import {getInjector, getLocalization} from 'core/svelte-context/context';
    import KpLoadingBlock from 'shared/components/kp-loading/KpLoadingBlock.svelte';
    import IconedContent from 'shared/ui-widgets/uicons/IconedContent.svelte';
    import {exists} from 'shared/utils/custom-utils';
    import {UserFilesUploadModalPresenter} from 'src/modals/user-files-upload-modal/user-files-upload-modal.presenter';

    export let model: UserFilesUploadModalModel;
    export let modalWindowActions: ModalWindowActions;

    const presenter = getInjector().getByToken<UserFilesUploadModalPresenter>(UserFilesUploadModalPresenter.presenterName);
    const localize = getLocalization();

    let uploadingFiles = false;

    const handleFilesUpload = (files: File[]) => {
        uploadingFiles = true;

        modalWindowActions.resolve(Promise.resolve(presenter.uploadFiles(model.user, files)));

        uploadingFiles = false;
    }

    let isDragging = false;
    let fileInputElement: HTMLInputElement;

    const handleDragOver = (event: DragEvent) => {
        event.preventDefault();

        if (uploadingFiles) {
            return;
        }

        if (isDraggingFile(event)) {
            isDragging = true;
        }
    };

    const handleDragLeave = (event: DragEvent) => {
        event.preventDefault();

        isDragging = false;
    };

    const handleDrop = (event: DragEvent) => {
        event.preventDefault();
        isDragging = false;

        if (uploadingFiles) {
            return;
        }

        const fileList = event.dataTransfer?.files;

        if (!exists(fileList)) {
            return;
        }

         handleFilesUpload(Array.from(fileList))
    };

    const isDraggingFile = (event: DragEvent): boolean => {
        const types = Array.from(event.dataTransfer?.types || []);
        return types.includes('Files');
    };

    const handleClick = () => {
        if (exists(fileInputElement)) {
            fileInputElement.click();
        }
    };

    const handleFileChange = (event: Event) => {
        const input = event.target as HTMLInputElement;
        const files = input.files ? Array.from(input.files) : [];

         handleFilesUpload(files);
    };
</script>

<KpModalContent {modalWindowActions} additionalClasses="files-selection-modal">
    <svelte:fragment slot="header">
        <KpModalHeaderCloseButton/>
        <KpModalTitle>
            {localize(/* @kp-localization file.ChooseFiles */ 'file.ChooseFiles')}
        </KpModalTitle>
    </svelte:fragment>

    <svelte:fragment slot="body">
        <button class="files-dropper"
                class:files-dragging={isDragging}
                class:uploading-files={uploadingFiles}
                on:click={handleClick}
                on:dragover={handleDragOver}
                on:dragleave={handleDragLeave}
                on:drop={handleDrop}>

            {#if uploadingFiles}
                <KpLoadingBlock size="xs"/>
            {/if}

            {#if !uploadingFiles}
                <div class="icon-container">
                    <IconedContent icon="upload" orientation="vertical" align="center" justify="center">
                        Zde nahrajte přetáhnutím nebo kliknutím soubory
                    </IconedContent>
                </div>
            {/if}

            <input class="hidden-input"
                   type="file"
                   bind:this={fileInputElement}
                   on:change={handleFileChange}
                   multiple/>
        </button>
    </svelte:fragment>

    <svelte:fragment slot="footer">
        <KpModalFooterCloseButton/>
    </svelte:fragment>
</KpModalContent>

<style lang="less">
    @import (reference) "styles/portaro.variables.less";

    .files-dropper {
        width: 95%;
        margin: 16px 0;
        align-self: center;
        max-width: 1000px;
        height: 200px;
        border-radius: @border-radius-default;
        background-color: #EAEEFF;
        outline: none;
        display: flex;
        align-items: center;
        justify-content: center;
        border: 2px dashed var(--accent-blue-new);
        transition: border 0.2s ease-in-out, background-color 0.2s ease-in-out;

        &.files-dragging,
        &.uploading-files,
        &:hover {
            border-color: var(--brand-orange-new);
            background-color: #F4E1DC;

            .icon-container {
                color: var(--brand-orange-new);
                transform: translateY(-2px);
            }
        }

        &.uploading-files .icon-container {
            transform: none;
        }

        .icon-container {
            color: var(--accent-blue-new);
            transition: transform 0.2s ease-in-out;
        }

        .hidden-input {
            display: none;
        }
    }
</style>