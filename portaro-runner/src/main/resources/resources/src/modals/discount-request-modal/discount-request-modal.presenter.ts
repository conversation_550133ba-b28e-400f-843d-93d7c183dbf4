import type FinishedResponseInteractionService from 'shared/services/finished-response-interaction.service';
import type {ActionResponse, User} from 'typings/portaro.be.types';
import type {ModalDialogService} from 'src/shared/modal-dialogs/modal-dialog.service';
import type UserService from 'src/features/user/user.service';
import type {LocalizationService} from 'shared/providers/localization.provider';
import {createFinishedResponseActionResponse} from 'src/modals/modal-utils';

export class DiscountRequestModalPresenter {
    public static presenterName = 'discountRequestModalPresenter'

    /*@ngInject*/
    constructor(private localizationService: LocalizationService,
                private userService: UserService,
                private finishedResponseInteractionService: FinishedResponseInteractionService,
                private modalDialogService: ModalDialogService) {
    }

    public async uploadUserFiles(user: User, showSuccessModal: boolean): Promise<ActionResponse> {
        try {
            await this.modalDialogService.openModalWindow('userFilesUpload', {user});
            const actionResponse = createFinishedResponseActionResponse(this.localizationService.get(/* @kp-localization discount.request.DiscountEligibilityMessage */ 'discount.request.DiscountEligibilityMessage'));
            if (showSuccessModal) {
                await this.finishedResponseInteractionService.showSuccessResponseInModalWindow(actionResponse);
            }
            return actionResponse;
        } catch (exceptionResponse) {
            await this.finishedResponseInteractionService.showFailedInModalWindow(exceptionResponse);
            return null;
        }
    }

    public async editIsic(user: User) {
        return this.userService.editIsic(user);
    }
}