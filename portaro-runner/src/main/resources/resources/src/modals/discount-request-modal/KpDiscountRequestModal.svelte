<script lang="ts">
    import type {DiscountRequestModalModel} from './types';
    import type {ModalWindowActions} from 'shared/modal-dialogs/types';
    import KpModalContent from '../kp-modal/KpModalContent.svelte';
    import KpModalFooterCloseButton from 'src/modals/kp-modal/KpModalFooterCloseButton.svelte';
    import KpModalHeaderCloseButton from 'src/modals/kp-modal/KpModalHeaderCloseButton.svelte';
    import KpModalTitle from 'src/modals/kp-modal/KpModalTitle.svelte';
    import KpButton from 'shared/ui-widgets/button/KpButton.svelte';
    import {getInjector, getLocalization} from 'core/svelte-context/context';
    import {DiscountRequestModalPresenter} from 'src/modals/discount-request-modal/discount-request-modal.presenter';
    import {getIdCardsByType} from 'shared/utils/user-utils';
    import type {Person} from 'typings/portaro.be.types';
    import {ContactType} from 'typings/portaro.be.types';

    export let model: DiscountRequestModalModel;
    export let modalWindowActions: ModalWindowActions;

    const presenter = getInjector().getByToken<DiscountRequestModalPresenter>(DiscountRequestModalPresenter.presenterName);
    const localize = getLocalization();

    $: isicIsNotPresent = getIdCardsByType(model.user as Person, ContactType.ISIC_CARD).length === 0

    let uploadingFiles = false;

    const handleFilesUpload = async () => {
        uploadingFiles = true;

        try {
            const filesUploaded = await presenter.uploadUserFiles(model.user, model.showSuccessModal);
            modalWindowActions.resolve(Promise.resolve(filesUploaded));
        } finally {
            uploadingFiles = false;
        }
    }

    const handleEditIsic = async () => {
        const editIsic = await presenter.editIsic(model.user);
        modalWindowActions.resolve(Promise.resolve(editIsic));
    }
</script>

<KpModalContent {modalWindowActions} additionalClasses="files-selection-modal">
    <svelte:fragment slot="header">
        <KpModalHeaderCloseButton/>
        <KpModalTitle>
            {localize(/* @kp-localization discount.request.Title */ 'discount.request.Title')}
        </KpModalTitle>
    </svelte:fragment>

    <svelte:fragment slot="body">
        <div class="discount-request">
            <div class="discount-option discount-cancel">
                <h2 class="discount-title">
                    {localize(/* @kp-localization discount.request.NoDiscountTitle */ 'discount.request.NoDiscountTitle')}
                </h2>
                <p class="discount-description">
                    {localize(/* @kp-localization discount.request.NoDiscountDescription */ 'discount.request.NoDiscountDescription')}
                </p>
                <KpButton buttonStyle="primary" on:click={() => modalWindowActions.acknowledge()} isBlock>
                    {localize(/* @kp-localization discount.request.ContinueWithoutDiscount */ 'discount.request.ContinueWithoutDiscount')}
                </KpButton>
            </div>
            {#if isicIsNotPresent}
                <div class="discount-option discount-isic-itic">
                    <h2 class="discount-title">
                        {localize(/* @kp-localization discount.request.StudentTeacherDiscountTitle */ 'discount.request.StudentTeacherDiscountTitle')}
                    </h2>
                    <p class="discount-description">
                        {localize(/* @kp-localization discount.request.StudentTeacherDiscountDescription */ 'discount.request.StudentTeacherDiscountDescription')}
                    </p>
                    <KpButton buttonStyle="primary" on:click={() => handleEditIsic()} isBlock>
                        {localize(/* @kp-localization discount.request.FillIsic */ 'discount.request.FillIsic')}
                    </KpButton>
                </div>
            {/if}
            <div class="discount-option discount-upload">
                <h2 class="discount-title">
                    {localize(/* @kp-localization discount.request.UploadFilesTitle */ 'discount.request.UploadFilesTitle')}
                </h2>
                <p class="discount-description">
                    {localize(/* @kp-localization discount.request.UploadFilesDescription */ 'discount.request.UploadFilesDescription')}
                </p>
                <ul class="discount-list">
                    <li class="discount-category">
                        <strong>{localize(/* @kp-localization discount.request.Blind.title */ 'discount.request.Blind.title')}</strong> –
                        {localize(/* @kp-localization discount.request.Blind.description */ 'discount.request.Blind.description')}
                    </li>
                    <li class="discount-category">
                        <strong>{localize(/* @kp-localization discount.request.Student.title */ 'discount.request.Student.title')}</strong> –
                        {localize(/* @kp-localization discount.request.Student.description */ 'discount.request.Student.description')}
                    </li>
                    <li class="discount-category">
                        <strong>{localize(/* @kp-localization discount.request.Teacher.title */ 'discount.request.Teacher.title')}</strong> –
                        {localize(/* @kp-localization discount.request.Teacher.description */ 'discount.request.Teacher.description')}
                    </li>
                    <li class="discount-category">
                        <strong>{localize(/* @kp-localization discount.request.ZTP.title */ 'discount.request.ZTP.title')}</strong> –
                        {localize(/* @kp-localization discount.request.ZTP.description */ 'discount.request.ZTP.description')}
                    </li>
                    <li class="discount-category">
                        <strong>{localize(/* @kp-localization discount.request.Other.title */ 'discount.request.Other.title')}</strong> –
                        {localize(/* @kp-localization discount.request.Other.description */ 'discount.request.Other.description')}
                    </li>
                </ul>
                <KpButton isDisabled="{uploadingFiles}" on:click={() => handleFilesUpload()} buttonStyle="primary" isBlock>
                    {localize(/* @kp-localization discount.request.UploadDocuments */ 'discount.request.UploadDocuments')}
                </KpButton>
            </div>
        </div>
    </svelte:fragment>

    <svelte:fragment slot="footer">
        <KpModalFooterCloseButton/>
    </svelte:fragment>
</KpModalContent>


<style lang="less">
    @import (reference) "styles/portaro.variables.less";

    .discount-request {
        margin: 12px 24px
    }

    .discount-option {
        padding: 15px;
        border-radius: 6px;
        margin-bottom: 15px;
    }

    .discount-isic-itic {
        background: #e3f2fd;
        border-left: 5px solid #2196f3;
    }

    .discount-upload {
        background: #e8f5e9;
        border-left: 5px solid #4caf50;
    }

    .discount-cancel {
        background: #f5f5f5;
        border-left: 5px solid #bdbdbd;
        padding: 20px;
        border-radius: 8px;
    }

    .discount-title {
        font-size: 1.4em;
        margin-bottom: 10px;
        color: #333;
    }

    .discount-description {
        font-size: 1em;
        margin-bottom: 15px;
        color: #555;
    }

    .discount-list {
        padding-left: 20px;
        margin-bottom: 15px;
    }

    .discount-category {
        margin-bottom: 5px;
    }

</style>