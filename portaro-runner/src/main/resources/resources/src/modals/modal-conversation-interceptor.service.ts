import {ModalDialogService} from 'shared/modal-dialogs/modal-dialog.service';
import type {IHttpInterceptor, IHttpResponse, IHttpService, IPromise, IRequestConfig} from 'angular';
import type {ExceptionActionResponseHandler} from 'shared/services/exception-action-response-handler';
import type {UserPreferencesService} from '../features/user/user-preferences/user-preferences.service';
import type {CatalogueSelectionLoginListener} from './catalogue-selection-login.listener';
import type {BrowserProgressBarService} from 'shared/components/kp-browser-progress-bar/browser-progress-bar.service';
import type {KpSettingsService} from '../features/settings/kp-settings.service';
import type LoginService from 'shared/login/login.service';
import {responseTypes} from 'shared/constants/portaro.constants';
import type {
    ActionResponse,
    Auth,
    DiscountRequestShowException,
    ExceptionActionResponse,
    FieldEnablingExceptionContinuation,
    SettingTypeIdContainingException,
    UserBalanceIsNotSufficientException,
    UserPreferencesException
} from 'typings/portaro.be.types';
import type PaymentDataService from '../features/payment/payment.data-service';
import {assertIsActionResponse, isActionResponse, isExceptionActionResponse, isIHttpResponse} from 'shared/utils/types-utils';
import type CurrentAuthService from 'src/shared/services/current-auth.service';
import {exists} from 'shared/utils/custom-utils';
import {SKIP_MODAL_WINDOW_INTERCEPTOR_HEADER} from './constants';
import {assertIsModalWindowType} from 'shared/modal-dialogs/utils';
import type {ModalWindowTypeToModelMap} from 'shared/modal-dialogs/config';

/**
 * @ngdoc service
 * @name modalConversationInterceptor
 * @module portaro.services
 *
 * @required $injector
 * @required currentAuth
 * @required progressBarService
 *
 * @description
 * Service serves as HTTP interceptor, it provides functions that angular applies to every http request/response.
 */
export default class ModalConversationInterceptorService implements IHttpInterceptor {
    public static readonly serviceName = 'modalConversationInterceptor';

    private isInitialized = false;
    private modalDialogService: ModalDialogService;
    private $http: IHttpService;
    private loginService: LoginService;
    private userPreferencesService: UserPreferencesService
    private settingsService: KpSettingsService
    private paymentDataService: PaymentDataService
    private currentAuthService: CurrentAuthService;
    private exceptionHandlers: Map<string, ExceptionActionResponseHandler> = new Map<string, ExceptionActionResponseHandler>();
    private pendingLoginProcess: Promise<Auth> = null;

    /*@ngInject*/
    constructor(private $injector: angular.auto.IInjectorService, private currentAuth: Auth, private browserProgressBarService: BrowserProgressBarService) {
    }

    /**
     * This function is called when angular sends http request
     * It needs to return RequestConfig or promise of RequestConfig
     */
    public request = (request: IRequestConfig): IRequestConfig => this.requestHandler(request);

    /**
     * This function is called when angular receives OK http response
     * It needs to return HttpResponse or promise of HttpResponse
     */
    public response = (response: IHttpResponse<any>): IPromise<angular.IHttpResponse<any>> | IHttpResponse<any> => this.responseHandler(response);

    /**
     * This function is called when angular receives ERROR http response
     * It needs to return HttpResponse or promise of HttpResponse
     */
    public responseError = (errorResponse: IHttpResponse<any>): Promise<IHttpResponse<any>> => this.responseErrorHandler(errorResponse);

    /**
     * Initialize dependencies when used for the first time.
     */
    private init() {
        if (!this.isInitialized) {
            this.modalDialogService = this.$injector.get<ModalDialogService>(ModalDialogService.serviceName);
            this.$http = this.$injector.get<IHttpService>('$http');
            this.loginService = this.$injector.get<LoginService>('loginService');
            this.userPreferencesService = this.$injector.get<UserPreferencesService>('userPreferencesService');
            this.settingsService = this.$injector.get<KpSettingsService>('kpSettingsService');
            this.paymentDataService = this.$injector.get<PaymentDataService>('paymentDataService');
            this.currentAuthService = this.$injector.get<CurrentAuthService>('currentAuthService');

            const catalogueSelectionLoginListener = this.$injector.get<CatalogueSelectionLoginListener>('catalogueSelectionLoginListener');
            this.loginService.registerListener(catalogueSelectionLoginListener);
            // TODO: when reload after login is no longer needed use this instead:
            // this.loginService.loginEventObservable$.subscribe((auth) => {
            //     catalogueSelectionLoginListener.update(auth);
            // });

            this.isInitialized = true;
        }
    }

    private requestHandler(request: IRequestConfig): IRequestConfig {
        this.init();
        this.browserProgressBarService.incrementPendingRequestsCounter();

        if (this.currentAuthService.getCurrentAuthValue().evided) {
            this.loginService.validityChecker.updateValidityInStorage();
        }

        return request;
    }

    private responseHandler(response: IHttpResponse<any>): IHttpResponse<any> | IPromise<IHttpResponse<ActionResponse>> {
        this.init();
        this.browserProgressBarService.decrementPendingRequestsCounter();

        if (isActionResponse(response.data)) {
            return this.actionResponseHandler(response); // open modal window based on the incoming ActionResponse if it is not ignored
        }
        return response; // return original response for other http responses
    }

    private responseErrorHandler(errorResponse: IHttpResponse<any>): Promise<IHttpResponse<any>> {
        this.init();
        this.browserProgressBarService.decrementPendingRequestsCounter();

        if (errorResponse.status === 401) {
            return this.notLoggedInHandler(errorResponse); // login and then repeat original request
        }
        if (isActionResponse(errorResponse.data)) {
            return this.actionResponseHandler(errorResponse); // open modal window based on the incoming ActionResponse if it is not ignored
        }
        return Promise.reject(errorResponse); // if not ActionResponse return rejected promise with original response
    }

    /**
     * Login and then repeat errored http call
     */
    private async notLoggedInHandler(errorResponse: IHttpResponse<any>): Promise<IHttpResponse<any>> {
        try {
            await this.login();
            return this.$http(errorResponse.config);
        } catch {
            throw errorResponse;
        }
    }

    /**
     * Open modal window based on the data in response
     */
    private async actionResponseHandler(response: IHttpResponse<ActionResponse>): Promise<IHttpResponse<ActionResponse>> {
        const actionResponse = response.data;

        if (isExceptionActionResponse(actionResponse) && this.exceptionHandlers.has(actionResponse.simpleName)) {
            const exceptionActionResponseHandler = this.exceptionHandlers.get(actionResponse.simpleName);
            try {
                return await exceptionActionResponseHandler(actionResponse, response);
            } catch (error) {
                if (isIHttpResponse(error)) {
                    throw error;
                }
                response.data = error; // save result from modal window to HttpResponse
                throw response;
            }
        }

        if (actionResponse.finished) {
            if (actionResponse.responseType === responseTypes.EXCEPTION) {
                return Promise.reject(response);
            }
            return Promise.resolve(response);
        }

        if (this.shouldSkipModalWindow(response)) {
            return response;
        }

        try {
            assertIsModalWindowType(actionResponse.responseType);
            const modalResponse = await this.modalDialogService.openModalWindow(actionResponse.responseType, actionResponse as ModalWindowTypeToModelMap[typeof actionResponse.responseType]);
            assertIsActionResponse(modalResponse);
            response.data = modalResponse// save result from modal window to HttpResponse
            return response;

        } catch (modalResponse) {
            assertIsActionResponse(modalResponse);
            response.data = modalResponse; // save result from modal window to HttpResponse
            throw response;
        }
    }

    public registerDataModifyingPasswordQuestionHandler(exceptionSimpleName: string, confirmationText: string, fieldName: string) {
        const dataModifier = async (originalRequestData: any) => {
            originalRequestData[fieldName] = await this.modalDialogService.openModalWindowAndGetPayload('passwordQuestion', {text: confirmationText});
            return originalRequestData;
        };
        this.registerDataModifyingRequestRepeater(exceptionSimpleName, dataModifier);
    }

    public registerFieldEnablingConfirmationDialogFromExceptionHandler(exceptionSimpleName: string) {
        const dataModifier = async (originalRequestData: any, exceptionActionResponse: FieldEnablingExceptionContinuation) => {
            await this.modalDialogService.openModalWindow('confirmationDialog', {text: exceptionActionResponse.continuation.confirmationText});
            originalRequestData[exceptionActionResponse.continuation.fieldToEnable] = true;
            return originalRequestData;
        };
        this.registerDataModifyingRequestRepeater(exceptionSimpleName, dataModifier);
    }

    public registerUserPreferenceExceptionHandler(exceptionSimpleName: string) {
        const dataModifier = async (originalRequestData: any, exceptionActionResponse: UserPreferencesException) => {
            await this.userPreferencesService.setUserPreferences(this.currentAuth.activeUser, exceptionActionResponse.userPreferenceKeys, exceptionActionResponse.text)
            return originalRequestData;
        };
        this.registerDataModifyingRequestRepeater(exceptionSimpleName, dataModifier);
    }

    public registerSettingExceptionHandler(exceptionSimpleName: string) {
        const dataModifier = async (originalRequestData: any, exceptionActionResponse: SettingTypeIdContainingException) => {
            await this.settingsService.setSettings([exceptionActionResponse.settingTypeId], exceptionActionResponse.text);
            return originalRequestData;
        };
        this.registerDataModifyingRequestRepeater(exceptionSimpleName, dataModifier);
    }

    public registerPaymentExceptionHandler(exceptionSimpleName: string) {
        const dataModifier = async (originalRequestData: any, exceptionActionResponse: UserBalanceIsNotSufficientException) => {
            await this.paymentDataService.pay({
                cashierDepartment: null,
                payer: exceptionActionResponse.payer,
                provider: exceptionActionResponse.provider,
                amounts: exceptionActionResponse.amounts
            })
            return originalRequestData;
        };
        this.registerDataModifyingRequestRepeater(exceptionSimpleName, dataModifier);
    }

    public registerDiscountRequestExceptionHandler(exceptionSimpleName: string) {
        const dataModifier = async (originalRequestData: any, exceptionActionResponse: DiscountRequestShowException) => {
            await this.modalDialogService.openModalWindow('discountRequest', {
                user: exceptionActionResponse.user,
                showSuccessModal: false
            })
            originalRequestData.ignoreDiscountRequest = true;
            return originalRequestData;
        };
        this.registerDataModifyingRequestRepeater(exceptionSimpleName, dataModifier);
    }

    private registerDataModifyingRequestRepeater<DATA>(exceptionSimpleName: string, dataModifier: (originalRequestData: DATA, exceptionActionResponse: ExceptionActionResponse) => Promise<DATA>) {
        const handler: ExceptionActionResponseHandler = async (exceptionActionResponse, response) => {
            response.config.data = await dataModifier(response.config.data, exceptionActionResponse);
            return this.$http(response.config);
        };
        this.registerExceptionActionResponseHandler(exceptionSimpleName, handler);
    }

    public registerExceptionActionResponseHandler(exceptionSimpleName: string, handler: ExceptionActionResponseHandler) {
        this.exceptionHandlers.set(exceptionSimpleName, handler);
    }

    private shouldSkipModalWindow(response: IHttpResponse<any>) {
        const requestHeader = response.config.headers?.[SKIP_MODAL_WINDOW_INTERCEPTOR_HEADER];
        if (exists(requestHeader) && requestHeader === 'true') {
            return true;
        }

        const responseHeader = response.headers(SKIP_MODAL_WINDOW_INTERCEPTOR_HEADER);

        return exists(responseHeader) && responseHeader === 'true';
    }

    private login() {
        if (this.isLoginProcessPending()) {
            return this.pendingLoginProcess;
        }
        return this.startLoginProcess();
    }

     private isLoginProcessPending() {
        return exists(this.pendingLoginProcess);
     }

     private async startLoginProcess() {
        this.pendingLoginProcess = this.loginService.login();
        try {
            return await this.pendingLoginProcess;
        } catch (error) {
            throw error;
        } finally {
            this.pendingLoginProcess = null;
        }
     }
}
