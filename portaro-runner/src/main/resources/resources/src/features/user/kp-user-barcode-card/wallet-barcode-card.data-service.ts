import type {BarcodeCardSettings} from 'src/features/user/kp-user-barcode-card/types';
import type {AjaxService} from 'core/data-services/ajax.service';
import type {FileDownloadService} from 'core/data-services/file-download.service';

export class WalletBarcodeCardDataService {
    public static serviceName = 'walletBarcodeCardDataService';

    public static readonly APPLE_ROUTE = 'wallet-barcode-card/generate/apple';
    public static readonly GOOGLE_ROUTE = 'wallet-barcode-card/generate/google';
    public static readonly CARD_SETTINGS = 'wallet-barcode-card/settings';

    /*@ngInject*/
    constructor(private ajaxService: AjaxService, private fileDownloadService: FileDownloadService) {
    }

    public async generateAppleWalletCard(): Promise<void> {
        return this.fileDownloadService
            .createRequest(WalletBarcodeCardDataService.APPLE_ROUTE)
            .download();
    }

    public async generateGoogleWalletCard(): Promise<Blob> {
        return this.ajaxService
            .createRequest(WalletBarcodeCardDataService.GOOGLE_ROUTE)
            .get();
    }

    public async getCardSettings(): Promise<BarcodeCardSettings> {
        return this.ajaxService
            .createRequest(WalletBarcodeCardDataService.CARD_SETTINGS)
            .get();
    }
}