import type {LocalizationService} from 'shared/providers/localization.provider';
import type {<PERSON>tha<PERSON>User, FinishedActionResponse, FinishedSaveResponse, RelationsRequest, User} from 'typings/portaro.be.types';
import {UserKind} from 'typings/portaro.be.types';
import type {DelayedReturnPenalty, UserId} from './types';
import type UserDataService from './user.data-service';
import type FinishedResponseInteractionService from 'shared/services/finished-response-interaction.service';
import type {ModalDialogService} from 'shared/modal-dialogs/modal-dialog.service';
import {ngAsync} from 'shared/utils/ng-@decorators';

export default class UserService {
    public static readonly CURRENT_USER_ALIAS = 'current';
    public static readonly serviceName = 'userService';

    /*@ngInject*/
    constructor(private userDataService: UserDataService,
                private finishedResponseInteractionService: FinishedResponseInteractionService,
                private localizationService: LocalizationService,
                private modalDialogService: ModalDialogService) {
    }

    public async getById(id: UserId): Promise<User> {
        try {
            return await this.userDataService.getUser(id);
        } catch (exceptionResponse) {
            await this.finishedResponseInteractionService.showFailedResponseInModalWindow(exceptionResponse);
            throw exceptionResponse;
        }
    }

    public getDelayedReturnPenalty(user: User): Promise<DelayedReturnPenalty> {
        return this.userDataService.getDelayedReturnPenalty(user.id);
    }

    public async extendRegistration(user: User): Promise<void> {
        try {
            const actionResponse = await this.userDataService.extendRegistration(user.id);
            await this.finishedResponseInteractionService.showSuccessResponseInToastIfPossible(actionResponse);
        } catch (exceptionResponse) {
            await this.finishedResponseInteractionService.showFailedResponseInModalWindow(exceptionResponse);
            throw exceptionResponse;
        }
    }

    public async editUserByForm(user: User): Promise<User> {
        try {
            const finishedSaveResponse = await this.userDataService.editUserByForm(user.id);
            await this.finishedResponseInteractionService.showSuccessResponseInModalWindow(finishedSaveResponse);
            return finishedSaveResponse.savedObject;
        } catch (exceptionResponse) {
            await this.finishedResponseInteractionService.showFailedResponseInModalWindow(exceptionResponse);
            throw exceptionResponse;
        }
    }

    public async editIsic(user: User): Promise<FinishedSaveResponse<User>> {
        try {
            const finishedSaveResponse = await this.userDataService.editSingleIsic(user.id);
            await this.finishedResponseInteractionService.showSuccessResponseInToastIfPossible(finishedSaveResponse);
            return finishedSaveResponse;
        } catch (exceptionResponse) {
            await this.finishedResponseInteractionService.showFailedResponseInModalWindow(exceptionResponse);
            throw exceptionResponse;
        }
    }

    @ngAsync()
    public async activateUser(user: AuthableUser) {
        try {
            const finishedSaveResponse = await this.userDataService.saveUser({
                id: user.id,
                kind: UserKind.USER, // FIXME: not working
                active: true
            });
            await this.finishedResponseInteractionService.showSuccessResponseInToastIfPossible(finishedSaveResponse);
            return finishedSaveResponse.savedObject;

        } catch (exceptionResponse) {
            await this.finishedResponseInteractionService.showFailedResponseInModalWindow(exceptionResponse);
            throw exceptionResponse;
        }
    }

    @ngAsync()
    public async createNewEditorPerson() {
        try {
            const finishedSaveResponse = await this.userDataService.saveUser({
                id: null,
                kind: UserKind.PERSON,
                readerAccounts: [{}],
                editorAccounts: [{}],
                active: true
            });
            await this.finishedResponseInteractionService.showSuccessResponseInModalWindow(finishedSaveResponse);
            return finishedSaveResponse.savedObject;

        } catch (exceptionResponse) {
            await this.finishedResponseInteractionService.showFailedResponseInModalWindow(exceptionResponse);
            throw exceptionResponse;
        }
    }

    @ngAsync()
    public async createNewPersonReader() {
        try {
            const finishedSaveResponse = await this.userDataService.saveUser({
                id: null,
                kind: UserKind.PERSON,
                readerAccounts: [{}],
                active: true
            });
            await this.finishedResponseInteractionService.showSuccessResponseInModalWindow(finishedSaveResponse);
            return finishedSaveResponse.savedObject;

        } catch (exceptionResponse) {
            await this.finishedResponseInteractionService.showFailedResponseInModalWindow(exceptionResponse);
            throw exceptionResponse;
        }
    }

    @ngAsync()
    public async createNewLibraryReader(): Promise<User> {
        try {
            const finishedSaveResponse = await this.userDataService.saveUser({
                id: null,
                kind: UserKind.LIBRARY,
                readerAccounts: [{}],
                active: true
            });
            await this.finishedResponseInteractionService.showSuccessResponseInModalWindow(finishedSaveResponse);
            return finishedSaveResponse.savedObject;

        } catch (exceptionResponse) {
            await this.finishedResponseInteractionService.showFailedResponseInModalWindow(exceptionResponse);
            throw exceptionResponse;
        }
    }

    @ngAsync()
    public async createNewInstitution(kind = UserKind.INSTITUTION): Promise<User> {
        try {
            const finishedSaveResponse = await this.userDataService.saveUser({
                id: null,
                kind,
                active: true
            });
            await this.finishedResponseInteractionService.showSuccessResponseInModalWindow(finishedSaveResponse);
            return finishedSaveResponse.savedObject;

        } catch (exceptionResponse) {
            await this.finishedResponseInteractionService.showFailedResponseInModalWindow(exceptionResponse);
            throw exceptionResponse;
        }
    }

    public async createNewFamily() {
        const family = await this.createNewInstitution(UserKind.FAMILY);
        return this.createNewRelation({target: family});
    }

    @ngAsync()
    public async createNewSoftware(): Promise<User> {
        try {
            const finishedSaveResponse = await this.userDataService.saveUser({
                id: null,
                kind: UserKind.SOFTWARE,
                active: true
            });
            await this.finishedResponseInteractionService.showSuccessResponseInModalWindow(finishedSaveResponse);
            return finishedSaveResponse.savedObject;

        } catch (exceptionResponse) {
            await this.finishedResponseInteractionService.showFailedResponseInModalWindow(exceptionResponse);
            throw exceptionResponse;
        }
    }

    @ngAsync()
    public async deleteUser(user: User): Promise<void> {
        try {
            await this.modalDialogService.openModalWindow('confirmationDialog', {text: this.localizationService.get(/* @kp-localization commons.ReallyDelete */ 'commons.ReallyDelete')});
            const actionResponse = await this.userDataService.removeUser(user.id);
            await this.finishedResponseInteractionService.showSuccessResponseInToastIfPossible(actionResponse);
        } catch (exceptionResponse) {
            await this.finishedResponseInteractionService.showFailedResponseInModalWindow(exceptionResponse);
            throw exceptionResponse;
        }
    }

    @ngAsync()
    public async deleteCredentials(user: User): Promise<void> {
        try {
            await this.modalDialogService.openModalWindow('confirmationDialog', {text: this.localizationService.get(/* @kp-localization commons.ReallyDelete */ 'commons.ReallyDelete')});
            const actionResponse = await this.userDataService.deleteUserCredentials(user.id);
            await this.finishedResponseInteractionService.showSuccessResponseInToastIfPossible(actionResponse);
        } catch (exceptionResponse) {
            await this.finishedResponseInteractionService.showFailedInModalWindow(exceptionResponse);
            throw exceptionResponse;
        }
    }

    @ngAsync()
    public async deleteLinkWithMojeID(user: User): Promise<void> {
        try {
            await this.modalDialogService.openModalWindow('confirmationDialog', {text: this.localizationService.get(/* @kp-localization commons.ReallyDelete */ 'commons.ReallyDelete')});
            const actionResponse = await this.userDataService.deleteLinkWithMojeID(user.id);
            await this.finishedResponseInteractionService.showSuccessResponseInToastIfPossible(actionResponse);
        } catch (exceptionResponse) {
            await this.finishedResponseInteractionService.showFailedInModalWindow(exceptionResponse);
            throw exceptionResponse;
        }
    }

    @ngAsync()
    public async deleteLinkWithBankID(user: User): Promise<void> {
        try {
            await this.modalDialogService.openModalWindow('confirmationDialog', {text: this.localizationService.get(/* @kp-localization commons.ReallyDelete */ 'commons.ReallyDelete')});
            const actionResponse = await this.userDataService.deleteLinkWithBankID(user.id);
            await this.finishedResponseInteractionService.showSuccessResponseInToastIfPossible(actionResponse);
        } catch (exceptionResponse) {
            await this.finishedResponseInteractionService.showFailedInModalWindow(exceptionResponse);
            throw exceptionResponse;
        }
    }


    @ngAsync()
    public async showPasswordChangeForm(user: User): Promise<void> {
        try {
            const result: FinishedActionResponse = await this.userDataService.changePassword({user: user.id});
            await this.finishedResponseInteractionService.showSuccessResponseInToastIfPossible(result);
        } catch (e) {
            await this.finishedResponseInteractionService.showFailedInModalWindow(e);
        }
    }

    @ngAsync()
    public async changePasswordMail() {
        try {
            const actionResponse = await this.userDataService.changePasswordMail();
            return await this.finishedResponseInteractionService.showSuccessResponseInModalWindow(actionResponse);
        } catch (exceptionResponse) {
            await this.finishedResponseInteractionService.showFailedInModalWindow(exceptionResponse);
            throw exceptionResponse;
        }
    }

    @ngAsync()
    public async createNewRelation(relationRequest: Partial<RelationsRequest> = {}) {
        try {
            const actionResponse = await this.userDataService.saveRelations(relationRequest);
            return await this.finishedResponseInteractionService.showSuccessResponseInModalWindow(actionResponse);
        } catch (exceptionResponse) {
            await this.finishedResponseInteractionService.showFailedInModalWindow(exceptionResponse);
            throw exceptionResponse;
        }
    }
}
