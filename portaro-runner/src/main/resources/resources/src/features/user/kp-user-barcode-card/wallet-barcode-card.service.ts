import type {BarcodeCardSettings} from 'src/features/user/kp-user-barcode-card/types';
import type {WalletBarcodeCardDataService} from 'src/features/user/kp-user-barcode-card/wallet-barcode-card.data-service';
import type FinishedResponseInteractionService from 'shared/services/finished-response-interaction.service';

export class WalletBarcodeCardService {
    public static readonly serviceName = 'walletBarcodeCardService';

    /*@ngInject*/
    constructor(private walletBarcodeCardDataService: WalletBarcodeCardDataService,
                private finishedResponseInteractionService: FinishedResponseInteractionService) {
    }

    public async generateAppleWalletCard(): Promise<void> {
        try {
            await this.walletBarcodeCardDataService.generateAppleWalletCard();
        } catch (exceptionResponse) {
            await this.finishedResponseInteractionService.showFailedResponseInModalWindow(exceptionResponse);
        }
    }

    public async generateGoogleWalletCard(): Promise<void> {
        try {
            await this.walletBarcodeCardDataService.generateGoogleWalletCard();
        } catch (exceptionResponse) {
            await this.finishedResponseInteractionService.showFailedResponseInModalWindow(exceptionResponse);
        }
    }

    public async getCardSettings(): Promise<BarcodeCardSettings> {
        try {
            return await this.walletBarcodeCardDataService.getCardSettings();
        } catch (exceptionResponse) {
            await this.finishedResponseInteractionService.showFailedResponseInModalWindow(exceptionResponse);
            return null;
        }
    }
}