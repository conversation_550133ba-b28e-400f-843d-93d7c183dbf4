import type {Message, UUID} from 'typings/portaro.be.types';
import type {AddThreadParticipantsRequest, GetUserThreadsRequest, NewThreadMessageRequest, NewThreadRequest, RemoveThreadParticipantsRequest, Thread, UpdateThreadRequest, UserUnreadMessagesCount} from '../types';
import type {AjaxService} from 'core/data-services/ajax.service';
import {ngAsync} from 'shared/utils/ng-@decorators';
import {transferify} from 'shared/utils/data-service-utils';
import type {UserId} from 'src/features/user/types';

export class ThreadsDataService {
    public static readonly serviceName = 'threadsDataService';

    public static readonly THREADS_ROUTE = 'threads';

    /*@ngInject*/
    constructor(private ajaxService: AjaxService) {
    }

    @ngAsync()
    public async createNewThreadMessage(message: NewThreadMessageRequest): Promise<Message> {
        return this.ajaxService
            .createRequest(`${ThreadsDataService.THREADS_ROUTE}/send`)
            .post(transferify(message));
    }

    @ngAsync()
    public async createNewThread(thread: NewThreadRequest): Promise<Thread> {
        return this.ajaxService
            .createRequest(`${ThreadsDataService.THREADS_ROUTE}/create`)
            .post(transferify(thread));
    }

    @ngAsync()
    public async getThreadById(id: UUID): Promise<Thread> {
        return this.ajaxService
            .createRequest(`${ThreadsDataService.THREADS_ROUTE}/${id}`)
            .get()
    }

    @ngAsync()
    public async getThreadByRecordId(recordId: UUID): Promise<Thread> {
        return this.ajaxService
            .createRequest(`${ThreadsDataService.THREADS_ROUTE}/record/${recordId}`)
            .get()
    }

    @ngAsync()
    public async getUserThread(thread: GetUserThreadsRequest): Promise<Thread[]> {
        return this.ajaxService
            .createRequest(`${ThreadsDataService.THREADS_ROUTE}`)
            .post(transferify(thread));
    }

    @ngAsync()
    public async getUserUnreadMessagesCount(userId: UserId): Promise<UserUnreadMessagesCount> {
        return this.ajaxService
            .createRequest(`${ThreadsDataService.THREADS_ROUTE}/${userId}/unread-messages-count`)
            .get();
    }

    @ngAsync()
    public async addThreadParticipants(request: AddThreadParticipantsRequest): Promise<Thread> {
        return this.ajaxService
            .createRequest(`${ThreadsDataService.THREADS_ROUTE}/add-participants`)
            .post(transferify(request));
    }

    @ngAsync()
    public async removeThreadParticipants(request: RemoveThreadParticipantsRequest): Promise<Thread> {
        return this.ajaxService
            .createRequest(`${ThreadsDataService.THREADS_ROUTE}/remove-participants`)
            .post(transferify(request));
    }

    @ngAsync()
    public async updateThread(request: UpdateThreadRequest): Promise<Thread> {
        return this.ajaxService
            .createRequest(`${ThreadsDataService.THREADS_ROUTE}/update`)
            .post(transferify(request));
    }
}