import type {AuthableUser, Identified, Message, SearchParams, User, UUID} from 'typings/portaro.be.types';

export type ThreadUser = User | AuthableUser | UUID;

export interface ThreadMessagesSearchParams extends SearchParams {
    thread: Thread;
}

export interface ThreadCreationSettings {
    type: 'group' | 'private';
    participants: ThreadUser[];
    name?: string;
}

export interface MessageReceivedEventData {
    threadId: UUID;
    message: Message;
}

// Entities
export interface Thread extends Identified<UUID> {
    name: string;
    participants: ThreadParticipant[];
    iconUrl?: string; // zatím žádn<PERSON> nem<PERSON>
}

export interface ThreadParticipant extends Identified<UUID> {
    user: User;
}

export interface UserUnreadMessagesCount {
    totalUnreadMessagesCount: number,
    threadUnreadMessagesCounts: ThreadUnreadMessagesCount[]
}

export interface ThreadUnreadMessagesCount{
    threadId: UUID;
    unreadMessagesCount: number;
}

// Requests
export interface NewThreadMessageRequest {
    thread: Thread;
    content: string;
    sender: User | AuthableUser;
}

export interface NewThreadRequest {
    name?: string;
    linkedRecord?: UUID;
    participants: ThreadUser[];
}

export interface GetUserThreadsRequest {
    user: User | AuthableUser;
}

export interface AddThreadParticipantsRequest {
    thread: Thread;
    participants: ThreadUser[];
}

export interface RemoveThreadParticipantsRequest {
    thread: Thread;
    participants: ThreadUser[];
}

export interface UpdateThreadRequest {
    thread: Thread;
    name: string;
}