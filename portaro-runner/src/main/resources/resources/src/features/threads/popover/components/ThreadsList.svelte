<script lang="ts">
    import type {Thread} from 'src/features/threads/types';
    import {createEventDispatcher, onD<PERSON>roy} from 'svelte';
    import {getThreadsContext} from 'src/features/threads/threads.context';
    import KpVerticalSeparator from 'shared/ui-widgets/separator/KpVerticalSeparator.svelte';
    import Kp<PERSON>utton from 'shared/ui-widgets/button/KpButton.svelte';
    import IconedContent from 'shared/ui-widgets/uicons/IconedContent.svelte';
    import ThreadChip from 'src/features/threads/popover/components/ThreadChip.svelte';

    export let selectedThread: Thread | null;

    const context = getThreadsContext();
    const dispatch = createEventDispatcher<{
        'new-thread': void,
        'thread-select': Thread
    }>();

    let userThreads: Thread[];
    const userThreadsUnsubscribe = context.userThreads.subscribe((currentUserThreads) => userThreads = currentUserThreads);

    onDestroy(() => {
        userThreadsUnsubscribe();
    });
</script>

<div class="threads-list">
    <span class="heading-text">Konverzace</span>

    <KpVerticalSeparator height="12px"/>

    {#each userThreads as thread(thread.id)}
        <ThreadChip {thread}
                    selected="{selectedThread?.id === thread.id}"
                    on:click={() => dispatch('thread-select', thread)}/>
    {/each}

    {#if userThreads.length > 0}
        <KpVerticalSeparator height="12px"/>
    {/if}

    <KpButton buttonSize="xs" buttonStyle="success-new" rounded on:click={() => dispatch('new-thread')}>
        <IconedContent icon="add">Nová konverzace</IconedContent>
    </KpButton>
</div>

<style lang="less">
    @import (reference) "styles/portaro.variables.less";
    @import (reference) "styles/portaro.themes.less";

    .threads-list {
        display: flex;
        align-items: center;
        flex-wrap: wrap;
        column-gap: @spacing-m;
        row-gap: @spacing-sm;
        padding: @spacing-ml;
        border-bottom: 1px solid @themed-border-default;

        .heading-text {
            font-weight: 500;
        }
    }
</style>