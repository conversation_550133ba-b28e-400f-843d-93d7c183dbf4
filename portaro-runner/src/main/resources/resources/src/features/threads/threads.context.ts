import type {Readable} from 'svelte/store';
import type {ThreadsService} from 'src/features/threads/services/threads.service';
import {writable} from 'svelte/store';
import {getContext, hasContext, setContext} from 'svelte';
import type {Thread} from 'src/features/threads/types';

const THREADS_CONTEXT_KEY = 'threads-context-key';

interface ThreadsContext {
    service: ThreadsService;
    userThreads: Readable<Thread[]>;
    setUserThreads: (threads: Thread[]) => void;
    addUserThread: (thread: Thread) => void;
}

export function createThreadsContext(service: ThreadsService): ThreadsContext {
    const userThreads = writable<Thread[]>([]);

    return setContext<ThreadsContext>(THREADS_CONTEXT_KEY, {
        service,
        userThreads,
        setUserThreads: (threads: Thread[]) => userThreads.set(threads),
        addUserThread: (thread: Thread) => userThreads.update((threads) => [...threads, thread])
    });
}

export function getThreadsContext(): ThreadsContext {
    if (!hasContext(THREADS_CONTEXT_KEY)) {
        throw new Error('Threads context does not exist! Use `createThreadsContext` function to create it in a parent component.');
    }

    return getContext<ThreadsContext>(THREADS_CONTEXT_KEY);
}
