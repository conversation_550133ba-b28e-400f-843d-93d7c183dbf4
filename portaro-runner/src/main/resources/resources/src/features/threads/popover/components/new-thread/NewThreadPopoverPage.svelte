<script lang="ts">
    import type {Thread} from 'src/features/threads/types';
    import type {TabButton, TabId} from 'shared/ui-widgets/tabset/types';
    import {createEventDispatcher} from 'svelte';
    import {getThreadsContext} from 'src/features/threads/threads.context';
    import {exists} from 'shared/utils/custom-utils';
    import {fade} from 'svelte/transition';
    import KpGenericTopbar from 'shared/components/kp-generic-topbar/KpGenericTopbar.svelte';
    import KpButton from 'shared/ui-widgets/button/KpButton.svelte';
    import IconedContent from 'shared/ui-widgets/uicons/IconedContent.svelte';
    import KpIconButton from 'shared/ui-widgets/button/KpIconButton.svelte';
    import KpButtonTabs from 'shared/ui-widgets/tabset/KpButtonTabs.svelte';
    import NewPrivateThread from './NewPrivateThread.svelte';
    import NewGroupThread from './NewGroupThread.svelte';

    const dispatch = createEventDispatcher<{'back': void, 'thread-created': Thread}>();
    const context = getThreadsContext();

    let threadName = '';
    let createdType: 'group' | 'private' = 'group';
    $: createDisabled = (createdType === 'group' && (!threadName || !threadName.trim())) || createdType === 'private';

    const handleCreateThreadClick = async () => {
        let createdThread: Thread;

        if (createdType === 'private') {
            // createdThread = await context.service.createPrivateThread(threadName, []);
        } else if (createdType === 'group') {
            createdThread = await context.service.createGroupThread(threadName, []);
        }

        if (!exists(createdThread)) {
            dispatch('back');
            return;
        }

        context.addUserThread(createdThread);
        dispatch('thread-created', createdThread);
    };

    const typeTabs: TabButton[] = [
        {
            id: 'tab-group',
            label: 'Skupinová konverzace',
            icon: 'group'
        },
        {
            id: 'tab-private',
            label: 'Soukromá konverzace',
            icon: 'lock'
        }
    ];

    const handleTypeChange = (event: CustomEvent<TabId>) => {
        threadName = '';
        createdType = event.detail === 'tab-group' ? 'group' : 'private';
    };
</script>

<div class="new-thread-popover-page">
    <KpGenericTopbar horizontalPadding="16px">
        <KpIconButton icon="arrow-small-left" noBackground on:click={() => dispatch('back')}/>
        <span class="heading-text">Nová konverzace</span>
    </KpGenericTopbar>

    <div class="new-thread-form-container">
        <KpButtonTabs tabButtons="{typeTabs}" fillWidth on:tab-change={handleTypeChange}/>

        {#key createdType}
            <div class="anim-container" in:fade={{duration: 250}}>
                {#if createdType === 'private'}
                    <NewPrivateThread/>
                {:else if createdType === 'group'}
                    <NewGroupThread bind:threadName/>
                {/if}
            </div>
        {/key}
    </div>

    <div class="create-button-container">
        {#if createdType === 'private'}
            <small class="text-muted text-center">
                Pokud již máte soukromou konverzaci s taným uživatelem, dojde pouze k otevření stávající konverzace
            </small>
        {/if}

        <KpButton buttonStyle="success-new" disabled="{createDisabled}" on:click={handleCreateThreadClick}>
            <IconedContent icon="add">
                {#if createdType === 'private'}
                    Vytvořit soukromou konverzaci
                {:else if createdType === 'group'}
                    Vytvořit skupinovou konverzaci
                {/if}
            </IconedContent>
        </KpButton>
    </div>
</div>

<style lang="less">
    @import (reference) "styles/portaro.variables.less";
    @import (reference) "styles/portaro.themes.less";
    @import (reference) "styles/portaro-erp.less";

    .new-thread-popover-page {
        .flex-grow();
        display: flex;
        flex-direction: column;

        .heading-text {
            font-weight: 500;
        }

        .new-thread-form-container {
            .flex-grow();
            gap: @spacing-xxl;
            padding: @spacing-ml;

            .anim-container {
                .flex-grow();
                gap: @spacing-xxl;
            }
        }

        .create-button-container {
            margin-top: auto;
            display: flex;
            flex-shrink: 0;
            flex-direction: column;
            align-items: center;
            gap: @spacing-sm;
            text-align: center;
            justify-content: center;
            padding: @spacing-ml;
            border-top: 1px solid @themed-border-default;
        }
    }
</style>