import type {UUID} from 'typings/portaro.be.types';
import type {UIcons} from 'shared/ui-widgets/uicons/types';

export type ToastType = 'info' | 'success' | 'error' | 'warning' | 'wait';

export interface ToastMessage {
    id: UUID;
    type: ToastType;
    title: string;
    message: string;
    timeout: number;
}

export interface ToastMessageTypeSettings {
    backgroundColor: string;
    icon?: UIcons;
    textColor?: string;
    borderColor?: string;
}